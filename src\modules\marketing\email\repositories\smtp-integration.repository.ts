import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../../../../shared/entities/integration.entity';
import { KeyPairEncryptionService } from '../../../../shared/services/encryption/key-pair-encryption.service';

/**
 * Interface cho SMTP server configuration
 */
export interface SmtpServerConfig {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  from: string;
  serverType: 'SMTP';
}

/**
 * Repository để xử lý SMTP email integrations
 */
@Injectable()
export class SmtpIntegrationRepository {
  private readonly logger = new Logger(SmtpIntegrationRepository.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    private readonly encryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * Lấy và giải mã SMTP integration
   * @param serverId UUID của integration
   * @returns SMTP server configuration đã giải mã
   */
  async findAndDecryptSmtpIntegration(serverId: string): Promise<SmtpServerConfig> {
    try {
      this.logger.debug(`Finding SMTP integration for serverId: ${serverId}`);

      // Lấy integration với provider information
      const integration = await this.integrationRepository
        .createQueryBuilder('integration')
        .leftJoinAndSelect('integration.integrationProvider', 'provider')
        .where('integration.id = :serverId', { serverId })
        .andWhere('integration.status = :status', { status: 'active' })
        .andWhere('provider.type = :providerType', { providerType: 'EMAIL_SMTP' })
        .getOne();

      if (!integration) {
        throw new Error(`SMTP integration không tồn tại hoặc không active: ${serverId}`);
      }

      // Giải mã encrypted config
      if (!integration.encryptedConfig || !integration.secretKey) {
        throw new Error(`SMTP integration thiếu encrypted config hoặc secret key: ${serverId}`);
      }

      const decryptedConfig = this.encryptionService.decryptObject(
        integration.encryptedConfig,
        integration.secretKey,
      ) as any;

      // Parse metadata
      const metadata = integration.metadata || {};

      // Build SMTP configuration
      const config: SmtpServerConfig = {
        host: (metadata as any).host || decryptedConfig.host,
        port: (metadata as any).port || decryptedConfig.port || 587,
        secure: (metadata as any).useSsl || decryptedConfig.useSsl || false,
        user: decryptedConfig.username || decryptedConfig.user,
        password: decryptedConfig.password,
        from: (metadata as any).senderEmail || decryptedConfig.from || decryptedConfig.username,
        serverType: 'SMTP',
      };

      // Validate required fields
      if (!config.host || !config.user || !config.password) {
        throw new Error('SMTP configuration thiếu thông tin bắt buộc: host, user, password');
      }

      this.logger.debug(`SMTP configuration resolved for serverId: ${serverId}`);
      return config;

    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy SMTP integration cho serverId ${serverId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem serverId có phải là SMTP integration không
   * @param serverId UUID của integration
   * @returns True nếu là SMTP integration
   */
  async isSmtpIntegration(serverId: string): Promise<boolean> {
    try {
      const integration = await this.integrationRepository
        .createQueryBuilder('integration')
        .leftJoinAndSelect('integration.integrationProvider', 'provider')
        .where('integration.id = :serverId', { serverId })
        .andWhere('integration.status = :status', { status: 'active' })
        .andWhere('provider.type = :providerType', { providerType: 'EMAIL_SMTP' })
        .getOne();

      return !!integration;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra SMTP integration cho serverId ${serverId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
