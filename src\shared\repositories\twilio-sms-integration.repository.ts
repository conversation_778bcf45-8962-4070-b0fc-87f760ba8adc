import { PaginatedResult } from '../response';
import { Injectable, Logger } from '@nestjs/common';
import { KeyPairEncryptionService } from '../services/encryption/key-pair-encryption.service';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ProviderEnum } from '../constants/provider.enum';
import { Integration, IntegrationProvider } from '../entities';
import { OwnedTypeEnum } from '../enums';
import { TwilioSmsMetadata } from '../interfaces/metadata.interface';

/**
 * Interface cho dữ liệu Twilio SMS đã giải mã
 */
export interface DecryptedTwilioSmsConfig {
  accountSid: string;
  authToken: string;
  fromPhone?: string;
  messagingServiceSid?: string;
}

/**
 * Interface cho kết quả tìm kiếm Twilio SMS Integration với dữ liệu đã giải mã
 */
export interface TwilioSmsIntegrationWithDecrypted {
  integration: Integration;
  decryptedConfig: DecryptedTwilioSmsConfig;
  metadata: TwilioSmsMetadata;
}

/**
 * Repository xử lý truy vấn dữ liệu cho Twilio SMS Integration
 */
@Injectable()
export class TwilioSmsIntegrationRepository extends Repository<Integration> {
  private readonly logger = new Logger(TwilioSmsIntegrationRepository.name);

  constructor(
    private dataSource: DataSource,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
  ) {
    super(Integration, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho Twilio SMS integration
   */
  private async createBaseQuery(): Promise<SelectQueryBuilder<Integration>> {
    const provider = await this.getTwilioSmsProvider();
    return this.createQueryBuilder('integration').where('integration.typeId = :typeId', {
      typeId: provider.id,
    });
  }

  /**
   * Lấy provider Twilio SMS
   */
  private async getTwilioSmsProvider(): Promise<IntegrationProvider> {
    const provider = await this.dataSource.getRepository(IntegrationProvider).findOne({
      where: { type: ProviderEnum.SMS_TWILIO },
    });

    if (!provider) {
      throw new Error('Twilio SMS provider không tồn tại');
    }

    return provider;
  }

  /**
   * Giải mã config của integration
   */
  private decryptConfig(integration: Integration): DecryptedTwilioSmsConfig {
    try {
      if (!integration.encryptedConfig || !integration.secretKey) {
        throw new Error('Không có dữ liệu mã hóa');
      }

      const decryptionResult = this.keyPairEncryptionService.decrypt(
        integration.encryptedConfig,
        integration.secretKey,
      );

      if (!decryptionResult.success) {
        throw new Error('Giải mã thất bại');
      }

      const decrypted = JSON.parse(decryptionResult.decryptedData);
      return decrypted as DecryptedTwilioSmsConfig;
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã config: ${error.message}`, error.stack);
      throw new Error(`Không thể giải mã cấu hình: ${error.message}`);
    }
  }

  /**
   * Tìm Twilio SMS integration theo ID với dữ liệu đã giải mã
   */
  async findByIdWithDecrypted(
    id: string,
    userId: number,
  ): Promise<TwilioSmsIntegrationWithDecrypted | null> {
    try {
      const queryBuilder = await this.createBaseQuery();
      const integration = await queryBuilder
        .andWhere('integration.id = :id', { id })
        .andWhere('integration.userId = :userId', { userId })
        .getOne();

      if (!integration) {
        return null;
      }

      const decryptedConfig = this.decryptConfig(integration);
      const metadata = integration.metadata as TwilioSmsMetadata;

      return {
        integration,
        decryptedConfig,
        metadata,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm Twilio SMS integration theo ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm Twilio SMS integration theo Account SID và User ID
   */
  async findByUserIdAndAccountSid(
    userId: number,
    accountSid: string,
  ): Promise<TwilioSmsIntegrationWithDecrypted | null> {
    try {
      const queryBuilder = await this.createBaseQuery();
      const integrations = await queryBuilder
        .andWhere('integration.userId = :userId', { userId })
        .getMany();

      // Tìm integration có accountSid khớp bằng cách giải mã từng cái
      for (const integration of integrations) {
        try {
          const decryptedConfig = this.decryptConfig(integration);
          if (decryptedConfig.accountSid === accountSid) {
            const metadata = integration.metadata as TwilioSmsMetadata;
            return {
              integration,
              decryptedConfig,
              metadata,
            };
          }
        } catch (error) {
          // Bỏ qua integration không giải mã được
          this.logger.warn(`Không thể giải mã integration ${integration.id}: ${error.message}`);
          continue;
        }
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm Twilio SMS integration theo accountSid: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
  
  /**
   * Xóa Twilio SMS integration
   */
  async deleteTwilioSmsIntegration(id: string, userId: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(Integration)
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .andWhere('typeId = (SELECT id FROM integration_providers WHERE type = :providerType)', {
          providerType: ProviderEnum.SMS_TWILIO,
        })
        .execute();

      return (result.affected || 0) > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa Twilio SMS integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa nhiều Twilio SMS integrations
   */
  async deleteMultipleTwilioSmsIntegrations(
    ids: string[],
    userId: number,
  ): Promise<{
    successIds: string[];
    failedIds: Array<{ id: string; reason: string }>;
  }> {
    const successIds: string[] = [];
    const failedIds: Array<{ id: string; reason: string }> = [];

    for (const id of ids) {
      try {
        const result = await this.deleteTwilioSmsIntegration(id, userId);
        if (result) {
          successIds.push(id);
        } else {
          failedIds.push({
            id,
            reason: 'Không tìm thấy tích hợp hoặc không có quyền xóa',
          });
        }
      } catch (error) {
        this.logger.error(
          `Lỗi khi xóa Twilio SMS integration ${id}: ${error.message}`,
          error.stack,
        );
        failedIds.push({
          id,
          reason: error.message || 'Lỗi không xác định',
        });
      }
    }

    return { successIds, failedIds };
  }

  /**
   * Lấy tất cả Twilio SMS integrations của user (không phân trang)
   */
  async findAllByUserId(userId: number): Promise<TwilioSmsIntegrationWithDecrypted[]> {
    try {
      const queryBuilder = await this.createBaseQuery();
      const integrations = await queryBuilder
        .andWhere('integration.userId = :userId', { userId })
        .orderBy('integration.createdAt', 'DESC')
        .getMany();

      const results: TwilioSmsIntegrationWithDecrypted[] = [];
      for (const integration of integrations) {
        try {
          const decryptedConfig = this.decryptConfig(integration);
          const metadata = integration.metadata as TwilioSmsMetadata;

          results.push({
            integration,
            decryptedConfig,
            metadata,
          });
        } catch (error) {
          this.logger.warn(`Không thể giải mã integration ${integration.id}: ${error.message}`);
          continue;
        }
      }

      return results;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy tất cả Twilio SMS integrations: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
