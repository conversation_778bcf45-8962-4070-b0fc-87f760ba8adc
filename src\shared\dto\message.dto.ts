import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsArray,
  IsObject,
  IsNumber,
  IsBoolean,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho loại tin nhắn
 */
export enum MessageType {
  TEXT = 'text', // Tin nhắn văn bản
  IMAGE = 'image', // Hình ảnh
  FILE = 'file', // File đính kèm
  AUDIO = 'audio', // File âm thanh
  VIDEO = 'video', // File video
  LOCATION = 'location', // Vị trí địa lý
  CONTACT = 'contact', // Thông tin liên hệ
  STICKER = 'sticker', // Sticker/emoji
  SYSTEM = 'system', // Tin nhắn hệ thống
  TEMPLATE = 'template', // Template message (ZNS, etc.)
  QUICK_REPLY = 'quick_reply', // Quick reply buttons
  CARD = 'card', // Card/carousel
  POLL = 'poll', // <PERSON><PERSON>nh chọn
  EVENT = 'event', // Sự kiện (join, leave, etc.)
}

/**
 * Enum cho trạng thái tin nhắn
 */
export enum MessageStatus {
  SENDING = 'sending', // Đang gửi
  SENT = 'sent', // Đã gửi
  DELIVERED = 'delivered', // Đã nhận
  READ = 'read', // Đã đọc
  FAILED = 'failed', // Gửi thất bại
  DELETED = 'deleted', // Đã xóa
}

/**
 * Enum cho hướng tin nhắn
 */
export enum MessageDirection {
  INBOUND = 'inbound', // Tin nhắn đến
  OUTBOUND = 'outbound', // Tin nhắn đi
}

/**
 * Enum cho vai trò người gửi
 */
export enum SenderRole {
  CUSTOMER = 'customer', // Khách hàng
  AGENT = 'agent', // AI
  SYSTEM = 'system', // Người dùng nhắn
}

/**
 * DTO cho attachment của tin nhắn
 */
export class MessageAttachmentDto {
  @ApiProperty({
    description: 'ID của attachment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Loại attachment',
    enum: ['image', 'file', 'audio', 'video'],
    example: 'image',
  })
  @IsEnum(['image', 'file', 'audio', 'video'])
  type: string;

  @ApiProperty({
    description: 'URL của file',
    example: 'https://example.com/files/image.jpg',
  })
  @IsString()
  url: string;

  @ApiProperty({
    description: 'Tên file gốc',
    example: 'screenshot.jpg',
  })
  @IsString()
  fileName: string;

  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1024000,
  })
  @IsNumber()
  fileSize: number;

  @ApiProperty({
    description: 'MIME type của file',
    example: 'image/jpeg',
  })
  @IsString()
  mimeType: string;

  @ApiProperty({
    description: 'Thumbnail URL (cho image/video)',
    example: 'https://example.com/thumbnails/thumb.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  thumbnailUrl?: string;

  @ApiProperty({
    description: 'Thời lượng (cho audio/video, tính bằng giây)',
    example: 120,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty({
    description: 'Kích thước ảnh/video (width x height)',
    example: { width: 1920, height: 1080 },
    required: false,
  })
  @IsOptional()
  @IsObject()
  dimensions?: { width: number; height: number };
}

/**
 * DTO cho quick reply button
 */
export class QuickReplyButtonDto {
  @ApiProperty({
    description: 'ID của button',
    example: 'btn_yes',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Text hiển thị trên button',
    example: 'Có',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Payload khi click button',
    example: 'PAYLOAD_YES',
  })
  @IsString()
  payload: string;

  @ApiProperty({
    description: 'Icon của button',
    example: '👍',
    required: false,
  })
  @IsOptional()
  @IsString()
  icon?: string;
}

/**
 * DTO cho template message
 */
export class TemplateMessageDto {
  @ApiProperty({
    description: 'ID của template',
    example: 'template_welcome',
  })
  @IsString()
  templateId: string;

  @ApiProperty({
    description: 'Tên template',
    example: 'Welcome Message',
  })
  @IsString()
  templateName: string;

  @ApiProperty({
    description: 'Tham số cho template',
    example: { customerName: 'Nguyễn Văn A', orderNumber: 'ORD123' },
  })
  @IsObject()
  parameters: Record<string, any>;

  @ApiProperty({
    description: 'Ngôn ngữ của template',
    example: 'vi',
    required: false,
  })
  @IsOptional()
  @IsString()
  language?: string;
}

/**
 * DTO cho location message
 */
export class LocationMessageDto {
  @ApiProperty({
    description: 'Vĩ độ',
    example: 21.0285,
  })
  @IsNumber()
  latitude: number;

  @ApiProperty({
    description: 'Kinh độ',
    example: 105.8542,
  })
  @IsNumber()
  longitude: number;

  @ApiProperty({
    description: 'Tên địa điểm',
    example: 'Hồ Hoàn Kiếm, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết',
    example: 'Phường Hàng Trống, Quận Hoàn Kiếm, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;
}

/**
 * DTO cho contact message
 */
export class ContactMessageDto {
  @ApiProperty({
    description: 'Tên liên hệ',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '+84901234567',
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({
    description: 'Email',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    description: 'Tổ chức/công ty',
    example: 'ABC Company',
    required: false,
  })
  @IsOptional()
  @IsString()
  organization?: string;
}

/**
 * DTO cho metadata của tin nhắn
 */
export class MessageMetadataDto {
  @ApiProperty({
    description: 'ID tin nhắn gốc (cho reply)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  replyToMessageId?: string;

  @ApiProperty({
    description: 'ID tin nhắn được forward',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  forwardedFromMessageId?: string;

  @ApiProperty({
    description: 'Thông tin người gửi gốc (cho forward)',
    example: { id: 'user123', name: 'Nguyễn Văn B' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  originalSender?: { id: string; name: string };

  @ApiProperty({
    description: 'Có phải tin nhắn được edit không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isEdited?: boolean;

  @ApiProperty({
    description: 'Thời gian edit cuối (timestamp)',
    example: 1703123456789,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  editedAt?: number;

  @ApiProperty({
    description: 'Thông tin platform cụ thể',
    example: { messageId: 'fb_msg_123', threadId: 'fb_thread_456' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  platformData?: Record<string, any>;

  @ApiProperty({
    description: 'Thẻ gắn cho tin nhắn',
    example: ['important', 'follow-up'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { source: 'mobile-app', version: '1.2.3' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  customData?: Record<string, any>;
}

/**
 * DTO tạo tin nhắn mới
 */
export class CreateMessageDto {
  @ApiProperty({
    description: 'ID cuộc trò chuyện',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  conversationId: string;

  @ApiProperty({
    description: 'Loại tin nhắn',
    enum: MessageType,
    example: MessageType.TEXT,
  })
  @IsEnum(MessageType)
  type: MessageType;

  @ApiProperty({
    description: 'Nội dung tin nhắn (cho text message)',
    example: 'Xin chào! Tôi cần hỗ trợ về sản phẩm.',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: 'Danh sách attachments',
    type: [MessageAttachmentDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageAttachmentDto)
  attachments?: MessageAttachmentDto[];

  @ApiProperty({
    description: 'Quick reply buttons',
    type: [QuickReplyButtonDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuickReplyButtonDto)
  quickReplies?: QuickReplyButtonDto[];

  @ApiProperty({
    description: 'Template message data',
    type: TemplateMessageDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TemplateMessageDto)
  templateData?: TemplateMessageDto;

  @ApiProperty({
    description: 'Location data',
    type: LocationMessageDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => LocationMessageDto)
  locationData?: LocationMessageDto;

  @ApiProperty({
    description: 'Contact data',
    type: ContactMessageDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ContactMessageDto)
  contactData?: ContactMessageDto;

  @ApiProperty({
    description: 'Metadata bổ sung',
    type: MessageMetadataDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MessageMetadataDto)
  metadata?: MessageMetadataDto;

  @ApiProperty({
    description: 'Hướng tin nhắn',
    enum: MessageDirection,
    example: MessageDirection.INBOUND,
    required: false,
  })
  @IsOptional()
  @IsEnum(MessageDirection)
  direction?: MessageDirection;
}

/**
 * DTO response cho tin nhắn
 */
export class MessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID cuộc trò chuyện',
    example: '123e4567-e89b-12d3-a456-************',
  })
  conversationId: string;

  @ApiProperty({
    description:
      'ID người gửi (UUID của CustomerPlatform, null cho OA messages)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  senderId?: string;

  @ApiProperty({
    description: 'Tên người gửi',
    example: 'Nguyễn Văn A',
  })
  senderName: string;

  @ApiProperty({
    description: 'Avatar người gửi',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  senderAvatar?: string;

  @ApiProperty({
    description: 'Loại tin nhắn',
    enum: MessageType,
    example: MessageType.TEXT,
  })
  type: MessageType;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào! Tôi cần hỗ trợ về sản phẩm.',
    required: false,
  })
  content?: string;

  @ApiProperty({
    description: 'Danh sách attachments',
    type: [MessageAttachmentDto],
    required: false,
  })
  attachments?: MessageAttachmentDto[];

  @ApiProperty({
    description: 'Quick reply buttons',
    type: [QuickReplyButtonDto],
    required: false,
  })
  quickReplies?: QuickReplyButtonDto[];

  @ApiProperty({
    description: 'Template message data',
    type: TemplateMessageDto,
    required: false,
  })
  templateData?: TemplateMessageDto;

  @ApiProperty({
    description: 'Location data',
    type: LocationMessageDto,
    required: false,
  })
  locationData?: LocationMessageDto;

  @ApiProperty({
    description: 'Contact data',
    type: ContactMessageDto,
    required: false,
  })
  contactData?: ContactMessageDto;

  @ApiProperty({
    description: 'Trạng thái tin nhắn',
    enum: MessageStatus,
    example: MessageStatus.SENT,
  })
  status: MessageStatus;

  @ApiProperty({
    description: 'Hướng tin nhắn',
    enum: MessageDirection,
    example: MessageDirection.INBOUND,
  })
  direction: MessageDirection;

  @ApiProperty({
    description: 'ID tin nhắn gốc (cho reply)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  replyToMessageId?: string;

  @ApiProperty({
    description: 'ID tin nhắn được forward',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  forwardedFromMessageId?: string;

  @ApiProperty({
    description: 'Thông tin người gửi gốc (cho forward)',
    example: { id: 'user123', name: 'Nguyễn Văn B' },
    required: false,
  })
  originalSender?: { id: string; name: string };

  @ApiProperty({
    description: 'Có phải tin nhắn được edit không',
    example: false,
    required: false,
  })
  isEdited?: boolean;

  @ApiProperty({
    description: 'Thời gian edit cuối (timestamp)',
    example: 1703123456789,
    required: false,
  })
  editedAt?: number;

  @ApiProperty({
    description: 'Dữ liệu đặc thù platform',
    example: { messageId: 'fb_msg_123', threadId: 'fb_thread_456' },
    required: false,
  })
  platformData?: Record<string, any>;

  @ApiProperty({
    description: 'Thẻ gắn cho tin nhắn',
    example: ['important', 'follow-up'],
    required: false,
  })
  tags?: string[];

  @ApiProperty({
    description: 'Metadata bổ sung',
    type: MessageMetadataDto,
    required: false,
  })
  metadata?: MessageMetadataDto;

  @ApiProperty({
    description: 'Thời gian gửi (timestamp)',
    example: 1703123456789,
  })
  sentAt: number;

  @ApiProperty({
    description: 'Thời gian nhận (timestamp)',
    example: 1703123456789,
    required: false,
  })
  deliveredAt?: number;

  @ApiProperty({
    description: 'Thời gian đọc (timestamp)',
    example: 1703123456789,
    required: false,
  })
  readAt?: number;

  @ApiProperty({
    description: 'ID người tạo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  createdBy: string;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1703123456789,
  })
  updatedAt: number;
}
