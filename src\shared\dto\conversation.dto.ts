import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsArray,
  IsObject,
  IsNumber,
  IsBoolean,
  IsUUID,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * Enum cho loại cuộc trò chuyện
 */
export enum ConversationType {
  DIRECT = 'direct', // Trò chuyện trực tiếp 1-1
  GROUP = 'group', // Nhóm chat
  CHANNEL = 'channel', // Kênh công khai
  SUPPORT = 'support', // Hỗ trợ khách hàng
  BOT = 'bot', // Chat với bot
}

/**
 * Enum cho trạng thái cuộc trò chuyện
 */
export enum ConversationStatus {
  ACTIVE = 'active', // Đang hoạt động
  ARCHIVED = 'archived', // Đã lưu trữ
  DELETED = 'deleted', // Đã xóa
  MUTED = 'muted', // Đã tắt thông báo
  BLOCKED = 'blocked', // Đã chặn
}

/**
 * Enum cho platform/kênh
 */
export enum Platform {
  WEBSITE = 'website', // Website chat widget
  FACEBOOK = 'facebook', // Facebook Messenger
  ZALO_OA = 'zalo_oa', // Zalo Official Account
  ZALO_PERSONAL = 'zalo_personal', // Zalo Personal
  TELEGRAM = 'telegram', // Telegram
  WHATSAPP = 'whatsapp', // WhatsApp
  EMAIL = 'email', // Email
  SMS = 'sms', // SMS
  INTERNAL = 'internal', // Hệ thống nội bộ
}

/**
 * DTO cho metadata của cuộc trò chuyện
 */
export class ConversationMetadataDto {
  @ApiProperty({
    description: 'Thông tin bổ sung về cuộc trò chuyện',
    example: { source: 'landing-page', campaign: 'summer-2024' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  customData?: Record<string, any>;

  @ApiProperty({
    description: 'Thẻ gắn cho cuộc trò chuyện',
    example: ['urgent', 'vip-customer', 'technical-support'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Thông tin platform cụ thể',
    example: { platform: 'zalo', userId: 'zalo_123', threadId: 'thread_456' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  platformData?: Record<string, any>;

  @ApiProperty({
    description: 'Độ ưu tiên của cuộc trò chuyện (1-5)',
    example: 3,
    minimum: 1,
    maximum: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  priority?: number;

  @ApiProperty({
    description: 'ID của agent được gán',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  assignedAgentId?: string;

  @ApiProperty({
    description: 'Thời gian phản hồi mong đợi (phút)',
    example: 30,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  expectedResponseTime?: number;
}

/**
 * DTO cho thông tin participant trong cuộc trò chuyện
 */
export class ConversationParticipantDto {
  @ApiProperty({
    description: 'ID của participant',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Tên hiển thị của participant',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  displayName: string;

  @ApiProperty({
    description: 'Avatar URL của participant',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @ApiProperty({
    description: 'Vai trò trong cuộc trò chuyện',
    example: 'member',
    enum: ['admin', 'moderator', 'member', 'guest'],
  })
  @IsEnum(['admin', 'moderator', 'member', 'guest'])
  role: string;

  @ApiProperty({
    description: 'Trạng thái online',
    example: true,
  })
  @IsBoolean()
  isOnline: boolean;

  @ApiProperty({
    description: 'Lần cuối online (timestamp)',
    example: 1703123456789,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  lastSeenAt?: number;
}

/**
 * DTO tạo cuộc trò chuyện mới
 */
export class CreateConversationDto {
  @ApiProperty({
    description: 'Tiêu đề cuộc trò chuyện',
    example: 'Hỗ trợ kỹ thuật - Khách hàng VIP',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Mô tả cuộc trò chuyện',
    example: 'Khách hàng cần hỗ trợ về tính năng thanh toán',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại cuộc trò chuyện',
    enum: ConversationType,
    example: ConversationType.SUPPORT,
  })
  @IsEnum(ConversationType)
  type: ConversationType;

  @ApiProperty({
    description: 'Platform/kênh của cuộc trò chuyện',
    enum: Platform,
    example: Platform.WEBSITE,
  })
  @IsEnum(Platform)
  platform: Platform;

  @ApiProperty({
    description: 'Danh sách ID participants',
    example: ['123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID(undefined, { each: true })
  participantIds: string[];

  @ApiProperty({
    description: 'Metadata bổ sung',
    type: ConversationMetadataDto,
    required: false,
  })
  @IsOptional()
  @Type(() => ConversationMetadataDto)
  metadata?: ConversationMetadataDto;

  @ApiProperty({
    description: 'ID của cuộc trò chuyện cha (cho thread)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  parentConversationId?: string;

  @ApiProperty({
    description: 'Có phải cuộc trò chuyện riêng tư không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;
}

/**
 * DTO cập nhật cuộc trò chuyện
 */
export class UpdateConversationDto {
  @ApiProperty({
    description: 'Tiêu đề cuộc trò chuyện',
    example: 'Hỗ trợ kỹ thuật - Đã giải quyết',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Mô tả cuộc trò chuyện',
    example: 'Đã hỗ trợ thành công về tính năng thanh toán',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Trạng thái cuộc trò chuyện',
    enum: ConversationStatus,
    example: ConversationStatus.ARCHIVED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ConversationStatus)
  status?: ConversationStatus;

  @ApiProperty({
    description: 'Metadata bổ sung',
    type: ConversationMetadataDto,
    required: false,
  })
  @IsOptional()
  @Type(() => ConversationMetadataDto)
  metadata?: ConversationMetadataDto;

  @ApiProperty({
    description: 'Có phải cuộc trò chuyện riêng tư không',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;
}

/**
 * DTO response cho cuộc trò chuyện
 */
export class ConversationResponseDto {
  @ApiProperty({
    description: 'ID của cuộc trò chuyện',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Tiêu đề cuộc trò chuyện',
    example: 'Hỗ trợ kỹ thuật - Khách hàng VIP',
  })
  title: string;

  @ApiProperty({
    description: 'Mô tả cuộc trò chuyện',
    example: 'Khách hàng cần hỗ trợ về tính năng thanh toán',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Loại cuộc trò chuyện',
    enum: ConversationType,
    example: ConversationType.SUPPORT,
  })
  type: ConversationType;

  @ApiProperty({
    description: 'Platform/kênh của cuộc trò chuyện',
    enum: Platform,
    example: Platform.WEBSITE,
  })
  platform: Platform;

  @ApiProperty({
    description: 'Trạng thái cuộc trò chuyện',
    enum: ConversationStatus,
    example: ConversationStatus.ACTIVE,
  })
  status: ConversationStatus;

  @ApiProperty({
    description: 'Danh sách participants',
    type: [ConversationParticipantDto],
  })
  participants: ConversationParticipantDto[];

  @ApiProperty({
    description: 'Metadata bổ sung',
    type: ConversationMetadataDto,
    required: false,
  })
  metadata?: ConversationMetadataDto;

  @ApiProperty({
    description: 'ID của cuộc trò chuyện cha',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  parentConversationId?: string;

  @ApiProperty({
    description: 'Số lượng tin nhắn chưa đọc',
    example: 5,
  })
  unreadCount: number;

  @ApiProperty({
    description: 'Tin nhắn cuối cùng',
    example: 'Cảm ơn bạn đã hỗ trợ!',
    required: false,
  })
  lastMessage?: string;

  @ApiProperty({
    description: 'Thời gian tin nhắn cuối (timestamp)',
    example: 1703123456789,
    required: false,
  })
  lastMessageAt?: number;

  @ApiProperty({
    description: 'Có phải cuộc trò chuyện riêng tư không',
    example: false,
  })
  isPrivate: boolean;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1703123456789,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'ID người tạo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  createdBy: string;
}

/**
 * DTO cho tạo group
 */
export class CreateGroupDto {
  @ApiProperty({
    description: 'Tên group',
    example: 'Nhóm hỗ trợ kỹ thuật',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Mô tả group',
    example: 'Nhóm xử lý các vấn đề kỹ thuật',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Platform của group',
    enum: Platform,
    example: Platform.WEBSITE,
  })
  @IsEnum(Platform)
  platform: Platform;

  @ApiProperty({
    description: 'Danh sách ID thành viên',
    example: ['123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID(undefined, { each: true })
  memberIds: string[];
}

/**
 * DTO cho cập nhật group
 */
export class UpdateGroupDto {
  @ApiProperty({
    description: 'Tên group',
    example: 'Nhóm hỗ trợ kỹ thuật - Cập nhật',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Mô tả group',
    example: 'Nhóm xử lý các vấn đề kỹ thuật - Đã cập nhật',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

/**
 * DTO response cho group
 */
export class GroupResponseDto {
  @ApiProperty({
    description: 'ID của group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Tên group',
    example: 'Nhóm hỗ trợ kỹ thuật',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả group',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Platform của group',
    enum: Platform,
  })
  platform: Platform;

  @ApiProperty({
    description: 'Số lượng thành viên',
    example: 5,
  })
  memberCount: number;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;

  @ApiProperty({
    description: 'ID người tạo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  createdBy: string;
}

/**
 * DTO cho thêm thành viên vào group
 */
export class AddGroupMemberDto {
  @ApiProperty({
    description: 'ID của thành viên',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  memberId: string;

  @ApiProperty({
    description: 'Vai trò trong group',
    example: 'member',
    enum: ['admin', 'moderator', 'member'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['admin', 'moderator', 'member'])
  role?: string;
}

/**
 * DTO cho cập nhật thành viên group
 */
export class UpdateGroupMemberDto {
  @ApiProperty({
    description: 'Vai trò mới trong group',
    example: 'moderator',
    enum: ['admin', 'moderator', 'member'],
  })
  @IsEnum(['admin', 'moderator', 'member'])
  role: string;
}

/**
 * DTO cho query conversations
 */
export class QueryConversationsDto {
  @ApiProperty({
    description: 'Platform để filter',
    enum: Platform,
    required: false,
  })
  @IsOptional()
  @IsEnum(Platform)
  platform?: Platform;

  @ApiProperty({
    description: 'Loại conversation để filter',
    enum: ConversationType,
    required: false,
  })
  @IsOptional()
  @IsEnum(ConversationType)
  type?: ConversationType;

  @ApiProperty({
    description: 'Trạng thái để filter',
    enum: ConversationStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ConversationStatus)
  status?: ConversationStatus;

  @ApiProperty({
    description: 'Số trang',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiProperty({
    description: 'Số item trên mỗi trang',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;
}

/**
 * DTO cho query messages
 */
export class QueryMessagesDto {
  @ApiProperty({
    description: 'ID conversation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  conversationId: string;

  @ApiProperty({
    description: 'Số trang',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiProperty({
    description: 'Số item trên mỗi trang',
    example: 50,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 50;
}

/**
 * DTO cho query groups
 */
export class QueryGroupsDto {
  @ApiProperty({
    description: 'Platform để filter',
    enum: Platform,
    required: false,
  })
  @IsOptional()
  @IsEnum(Platform)
  platform?: Platform;

  @ApiProperty({
    description: 'Số trang',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiProperty({
    description: 'Số item trên mỗi trang',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;
}
