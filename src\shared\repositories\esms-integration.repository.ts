import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { AppException, ErrorCode } from '@common/exceptions';
import { KeyPairEncryptionService } from '../services/encryption/key-pair-encryption.service';
import { ESmsService } from '../services/e-sms/e-sms.service';
import { ProviderEnum } from '../constants';
import { Integration, IntegrationProvider } from '../entities';
import { IntegrationProviderRepository } from './integration-provider.repository';

/**
 * Interface cho dữ liệu mã hóa eSMS
 */
interface ESmsEncryptedConfig {
  apiKey: string;
  secretKey: string;
}

/**
 * Repository chuyên biệt cho eSMS Integration
 * Extend từ Repository<Integration> để có đầy đủ các method cơ bản
 */
@Injectable()
export class ESmsIntegrationRepository extends Repository<Integration> {
  private readonly logger = new Logger(ESmsIntegrationRepository.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
    private readonly integrationProviderRepository: IntegrationProviderRepository,
    private readonly eSmsService: ESmsService,
  ) {
    super(
      integrationRepository.target,
      integrationRepository.manager,
      integrationRepository.queryRunner,
    );
  }

  /**
   * Lấy tất cả IntegrationProvider cho eSMS
   */
  private async getAllESmsProviders(): Promise<IntegrationProvider[]> {
    const providerTypes = [
      ProviderEnum.SMS_ESMS,
      ProviderEnum.SMS_ESMS_ADVERTISING,
      ProviderEnum.SMS_ESMS_CUSTOMER_CARE,
      ProviderEnum.SMS_ESMS_VIBER,
    ];

    const providers = await Promise.all(
      providerTypes.map(type => this.integrationProviderRepository.findByType(type))
    );

    const validProviders = providers.filter(provider => provider !== null) as IntegrationProvider[];

    if (validProviders.length === 0) {
      throw new AppException(
        ErrorCode.BAD_REQUEST,
        'Không tìm thấy IntegrationProvider nào cho eSMS',
      );
    }

    return validProviders;
  }

  /**
   * Tìm eSMS integration theo ID và userId
   */
  async findESmsIntegrationById(id: string, userId: number): Promise<Integration | null> {
    try {
      // Lấy tất cả eSMS providers để tìm trong tất cả các type
      const providers = await this.getAllESmsProviders();
      const providerIds = providers.map(p => p.id);

      return await this.findOne({
        where: {
          id,
          typeId: In(providerIds),
          userId,
        },
        relations: ['integrationProvider'],
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm eSMS integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa eSMS integration
   */
  async deleteESmsIntegration(id: string, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa eSMS integration ${id} cho user ${userId}`);

      // Lấy tất cả eSMS providers để xóa trong tất cả các type
      const providers = await this.getAllESmsProviders();
      const providerIds = providers.map(p => p.id);

      const result = await this.delete({
        id,
        typeId: In(providerIds),
        userId,
      });

      if (result.affected === 0) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Không tìm thấy eSMS integration để xóa',
        );
      }

      this.logger.log(`Đã xóa eSMS integration thành công: ${id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa eSMS integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy tất cả eSMS integrations của user (không phân trang)
   */
  async findAllESmsIntegrations(userId: number): Promise<Integration[]> {
    try {
      const providers = await this.getAllESmsProviders();
      const providerIds = providers.map(p => p.id);

      return await this.find({
        where: {
          typeId: In(providerIds),
          userId,
        },
        relations: ['integrationProvider'],
        order: {
          createdAt: 'DESC',
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy tất cả eSMS integrations: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm kiếm eSMS integrations theo nhiều tiêu chí
   */
  async searchESmsIntegrations(
    userId: number,
    searchCriteria: {
      integrationName?: string;
      brandname?: string;
      brandnameType?: number;
      typeId?: number;
      limit?: number;
      offset?: number;
    }
  ): Promise<Integration[]> {
    try {
      this.logger.log(`Tìm kiếm eSMS integrations cho user ${userId}`, searchCriteria);

      const queryBuilder = this.createQueryBuilder('integration')
        .leftJoinAndSelect('integration.integrationProvider', 'provider')
        .where('integration.userId = :userId', { userId });

      // Lọc theo typeId nếu có
      if (searchCriteria.typeId) {
        queryBuilder.andWhere('integration.typeId = :typeId', { typeId: searchCriteria.typeId });
      } else {
        // Mặc định chỉ lấy các eSMS providers
        const eSmsProviderTypes = [
          ProviderEnum.SMS_ESMS,
          ProviderEnum.SMS_ESMS_ADVERTISING,
          ProviderEnum.SMS_ESMS_CUSTOMER_CARE,
          ProviderEnum.SMS_ESMS_VIBER,
        ];

        const providers = await this.integrationProviderRepository.find({
          where: eSmsProviderTypes.map(type => ({ type }))
        });

        const providerIds = providers.map((p: IntegrationProvider) => p.id);
        if (providerIds.length > 0) {
          queryBuilder.andWhere('integration.typeId IN (:...providerIds)', { providerIds });
        }
      }

      // Tìm kiếm theo tên integration
      if (searchCriteria.integrationName) {
        queryBuilder.andWhere('integration.integrationName ILIKE :integrationName', {
          integrationName: `%${searchCriteria.integrationName}%`,
        });
      }

      // Lọc theo brandname trong metadata
      if (searchCriteria.brandname) {
        queryBuilder.andWhere("integration.metadata->>'brandname' ILIKE :brandname", {
          brandname: `%${searchCriteria.brandname}%`,
        });
      }

      // Lọc theo brandnameType trong metadata
      if (searchCriteria.brandnameType !== undefined) {
        queryBuilder.andWhere("(integration.metadata->>'brandnameType')::int = :brandnameType", {
          brandnameType: searchCriteria.brandnameType,
        });
      }

      // Sắp xếp theo thời gian tạo mới nhất
      queryBuilder.orderBy('integration.createdAt', 'DESC');

      // Phân trang
      if (searchCriteria.limit) {
        queryBuilder.take(searchCriteria.limit);
      }
      if (searchCriteria.offset) {
        queryBuilder.skip(searchCriteria.offset);
      }

      const integrations = await queryBuilder.getMany();

      this.logger.log(`Tìm thấy ${integrations.length} eSMS integrations`);
      return integrations;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm eSMS integrations: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Giải mã cấu hình eSMS từ integration
   */
  decryptESmsConfig(integration: Integration): ESmsEncryptedConfig | null {
    try {
      if (!integration.encryptedConfig || !integration.secretKey) {
        this.logger.warn(`Integration ${integration.id} không có encrypted config hoặc secret key`);
        return null;
      }

      const decryptionResult = this.keyPairEncryptionService.decrypt(
        integration.encryptedConfig,
        integration.secretKey
      );

      if (!decryptionResult.success) {
        this.logger.warn(`Không thể giải mã config cho integration ${integration.id}`);
        return null;
      }

      const decryptedData = JSON.parse(decryptionResult.decryptedData) as ESmsEncryptedConfig;

      // Validate decrypted data
      if (!decryptedData.apiKey || !decryptedData.secretKey) {
        this.logger.warn(`Dữ liệu giải mã không hợp lệ cho integration ${integration.id}`);
        return null;
      }

      return decryptedData;
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã config cho integration ${integration.id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm và giải mã eSMS integration theo ID
   */
  async findAndDecryptESmsIntegration(
    id: string,
    userId: number
  ): Promise<{ integration: Integration; config: ESmsEncryptedConfig } | null> {
    try {
      const integration = await this.findESmsIntegrationById(id, userId);
      if (!integration) {
        return null;
      }

      const config = this.decryptESmsConfig(integration);
      if (!config) {
        throw new AppException(
          ErrorCode.BAD_REQUEST,
          'Không thể giải mã cấu hình eSMS',
          { integrationId: id, userId }
        );
      }

      return { integration, config };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm và giải mã eSMS integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách integrations với config đã giải mã
   */
  async findESmsIntegrationsWithDecryptedConfig(
    userId: number,
    searchCriteria?: {
      integrationName?: string;
      brandname?: string;
      brandnameType?: number;
      limit?: number;
      offset?: number;
    }
  ): Promise<Array<{ integration: Integration; config: ESmsEncryptedConfig | null }>> {
    try {
      const integrations = await this.searchESmsIntegrations(userId, searchCriteria || {});

      const results = integrations.map(integration => ({
        integration,
        config: this.decryptESmsConfig(integration)
      }));

      // Lọc ra những integration có config hợp lệ nếu cần
      return results;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy eSMS integrations với config đã giải mã: ${error.message}`, error.stack);
      throw error;
    }
  }
}
