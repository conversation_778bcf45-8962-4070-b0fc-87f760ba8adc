import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from 'src/shared/queue';
import { SmsRecipientDto, SmsOtpJobDto, SmsAdsJobDto } from './dto';
import { SmsCampaignType } from './constants';
import { SenderTypeEnum } from 'src/shared/enums';
import { SmsMarketingService } from './services';
import { SmsProviderType } from '../interfaces/sms-provider-config.interface';
import { FptSmsBrandnameService } from '../../../shared/services/sms/fpt-sms-brandname.service';
import { SmsService } from '../../../shared/services/sms/sms.service';
import { SmsEncryptionService } from '../../sms_system/services/sms-encryption.service';
import {
  SmsMarketingHistoryData,
  SmsSendResult,
} from './interfaces/sms-marketing-history.interface';
import { ESmsService } from '../../../shared/services/e-sms/e-sms.service';
import { FptSmsIntegrationRepository } from '../../../shared/repositories/fpt-sms-integration.repository';
import { ESmsIntegrationRepository } from '../../../shared/repositories/esms-integration.repository';
import { TwilioSmsIntegrationRepository, DecryptedTwilioSmsConfig } from '../../../shared/repositories/twilio-sms-integration.repository';

/**
 * Union type cho các integration types
 */
type IntegrationResult =
  | { integration: any; decrypted: { clientId: string; clientSecret: string }; brandName: string; endpoint: string } // FPT SMS
  | { integration: any; config: { apiKey: string; secretKey: string } } // eSMS
  | { integration: any; decryptedConfig: DecryptedTwilioSmsConfig; metadata: any }; // Twilio

/**
 * Interface cho kết quả bulk SMS
 */
interface BulkSmsResult {
  success: boolean;
  results?: Array<{
    success: boolean;
    messageId?: string;
    errorMessage?: string;
    errorCode?: string;
  }>;
  brandName?: string;
  totalSent?: number;
  totalFailed?: number;
  errorMessage?: string;
  messageId?: string;
  campaignCode?: string;
}

/**
 * Enum định nghĩa các provider types cho SMS
 */
export enum SmsProviderTypeEnum {
  SMS_ESMS_ADVERTISING = 'SMS_ESMS_ADVERTISING',
  SMS_ESMS_CUSTOMER_CARE = 'SMS_ESMS_CUSTOMER_CARE',
  SMS_FPT = 'SMS_FPT',
  SMS_TWILIO = 'SMS_TWILIO',
}

/**
 * Enum định nghĩa các tên job SMS marketing mới
 */
export enum SmsJobName {
  /**
   * Job gửi SMS marketing OTP (từng phone là 1 job)
   */
  SMS_MARKETING_OTP = 'sms-marketing-otp',

  /**
   * Job gửi SMS marketing ADS (cả list phone là 1 job)
   */
  SMS_MARKETING_ADS = 'sms-marketing-ads',
}

/**
 * Processor xử lý queue SMS marketing campaigns
 */
@Injectable()
@Processor(QueueName.SMS_MARKETING, { concurrency: 5 })
export class SmsCampaignProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsCampaignProcessor.name);

  constructor(
    private readonly smsMarketingService: SmsMarketingService,
    private readonly fptSmsBrandnameService: FptSmsBrandnameService,
    private readonly smsService: SmsService,
    private readonly smsEncryptionService: SmsEncryptionService,
    private readonly eSmsService: ESmsService,
    private readonly fptSmsIntegrationRepository: FptSmsIntegrationRepository,
    private readonly eSmsIntegrationRepository: ESmsIntegrationRepository,
    private readonly twilioSmsIntegrationRepository: TwilioSmsIntegrationRepository,
  ) {
    super();
  }

  /**
   * Xử lý job SMS marketing - router chính
   * @param job Job từ queue
   */
  async process(job: Job<SmsOtpJobDto | SmsAdsJobDto>): Promise<void> {
    // Route job theo job name
    if (job.name === SmsJobName.SMS_MARKETING_OTP) {
      return this.processOtpJob(job as Job<SmsOtpJobDto>);
    } else if (job.name === SmsJobName.SMS_MARKETING_ADS) {
      return this.processAdsJob(job as Job<SmsAdsJobDto>);
    } else {
      this.logger.warn(`Unknown job name: ${job.name}`);
      return; // Bỏ qua job không phải SMS marketing
    }
  }

  /**
   * Xử lý job SMS marketing OTP (single recipient)
   * @param job Job từ queue
   */
  async processOtpJob(job: Job<SmsOtpJobDto>): Promise<void> {
    const jobData = job.data;
    
    this.logger.log(
      `Processing SMS OTP job: ${job.id} for campaign ${jobData.campaignId} to ${this.getFullPhoneNumber(jobData.recipient)}`,
    );

    try {
      // Validate job data
      if (!this.validateOtpJobData(jobData)) {
        throw new Error('Invalid OTP job data');
      }

      // Cập nhật trạng thái campaign thành SENDING
      await this.smsMarketingService.updateCampaignStatus(
        jobData.campaignId,
        'SENDING',
        { startedAt: Date.now() },
      );

      // Lấy provider type từ job data
      if (!jobData.providerType || !Object.values(SmsProviderTypeEnum).includes(jobData.providerType as SmsProviderTypeEnum)) {
        throw new Error('Provider type is missing or invalid in job data');
      }
      const providerType = jobData.providerType as SmsProviderTypeEnum;

      // Gửi SMS OTP theo provider type
      let sendResult: SmsSendResult;
      const fullPhone = this.getFullPhoneNumber(jobData.recipient);

      switch (providerType) {
        case SmsProviderTypeEnum.SMS_FPT:
          sendResult = await this.sendFptOtpSms(
            fullPhone,
            jobData.content || '',
            jobData.serverId,
            jobData.userId,
            jobData.campaignName,
            jobData.scheduledAt,
          );
          break;

        case SmsProviderTypeEnum.SMS_ESMS_CUSTOMER_CARE:
          sendResult = await this.sendEsmsOtpSms(
            fullPhone,
            jobData.content || '',
            jobData.serverId,
            jobData.userId,
            jobData.campaignName || 'SMS_Campaign',
          );
          break;

        case SmsProviderTypeEnum.SMS_TWILIO:
          sendResult = await this.sendTwilioOtpSms(
            fullPhone,
            jobData.content || '',
            jobData.serverId,
            jobData.userId,
          );
          break;

        default:
          throw new Error(`Unsupported provider type for OTP: ${providerType}`);
      }

      // Lưu lịch sử và cập nhật campaign status
      await this.saveSingleSmsHistory(jobData, sendResult, providerType);
      await this.updateCampaignAfterSend(jobData.campaignId, sendResult.success ? 1 : 0, sendResult.success ? 0 : 1);

      this.logger.log(
        `SMS OTP job ${job.id} completed: ${sendResult.success ? 'success' : 'failed'}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing SMS OTP job ${job.id}: ${error.message}`,
        error.stack,
      );

      await this.smsMarketingService.updateCampaignStatus(
        jobData.campaignId,
        'FAILED',
        { completedAt: Date.now() },
      );

      throw error;
    }
  }

  /**
   * Xử lý job SMS marketing ADS (multiple recipients)
   * @param job Job từ queue
   */
  async processAdsJob(job: Job<SmsAdsJobDto>): Promise<void> {
    const jobData = job.data;
    
    this.logger.log(
      `Processing SMS ADS job: ${job.id} for campaign ${jobData.campaignId} with ${jobData.recipients.length} recipients`,
    );

    try {
      // Validate job data
      if (!this.validateAdsJobData(jobData)) {
        throw new Error('Invalid ADS job data');
      }

      // Cập nhật trạng thái campaign thành SENDING
      await this.smsMarketingService.updateCampaignStatus(
        jobData.campaignId,
        'SENDING',
        { startedAt: Date.now() },
      );

      // Xử lý gửi bulk SMS theo provider
      let successCount = 0;
      let failedCount = 0;
      const smsHistories: SmsMarketingHistoryData[] = [];
      const sentAt = Date.now();

      // Lấy provider type từ job data
      if (!jobData.providerType || !Object.values(SmsProviderTypeEnum).includes(jobData.providerType as SmsProviderTypeEnum)) {
        throw new Error('Provider type is missing or invalid in job data');
      }
      const providerType = jobData.providerType as SmsProviderTypeEnum;

      this.logger.log(`Processing ADS campaign with provider: ${providerType}`);

      // Gửi bulk SMS theo provider - mỗi method tự lấy integration
      let bulkResult: BulkSmsResult;
      try {
        switch (providerType) {
          case SmsProviderTypeEnum.SMS_ESMS_ADVERTISING:
            bulkResult = await this.sendBulkEsmsAdsSms(
              jobData.recipients,
              jobData.content || '',
              jobData.serverId,
              jobData.userId,
              jobData.campaignName || 'SMS_Campaign',
            );
            break;

          case SmsProviderTypeEnum.SMS_FPT:
            bulkResult = await this.sendBulkFptAdsSms(
              jobData.recipients,
              jobData.content || '',
              jobData.serverId,
              jobData.userId,
              jobData.campaignName,
              jobData.scheduledAt,
            );
            break;

          case SmsProviderTypeEnum.SMS_TWILIO:
            bulkResult = await this.sendBulkTwilioAdsSms(
              jobData.recipients,
              jobData.content || '',
              jobData.serverId,
              jobData.userId,
            );
            break;

          default:
            throw new Error(`Unsupported provider type for ADS: ${providerType}. Only ${SmsProviderTypeEnum.SMS_ESMS_ADVERTISING}, ${SmsProviderTypeEnum.SMS_FPT}, and ${SmsProviderTypeEnum.SMS_TWILIO} are supported for ADS campaigns.`);
        }

        // Xử lý kết quả bulk SMS
        for (let i = 0; i < jobData.recipients.length; i++) {
          const recipient = jobData.recipients[i];
          const recipientResult = bulkResult.results?.[i] || {
            success: bulkResult.success,
            messageId: bulkResult.messageId,
            errorMessage: bulkResult.errorMessage,
          };

          if (recipientResult.success) {
            successCount++;
          } else {
            failedCount++;
          }

          const historyData: SmsMarketingHistoryData = {
            campaignId: jobData.campaignId,
            recipientPhone: this.getFullPhoneNumber(recipient),
            recipientName: '',
            message: jobData.content || '',
            content: jobData.content || '',
            campaignType: String(jobData.campaignType),
            status: recipientResult.success ? 'sent' : 'failed',
            sentAt,
            messageId: recipientResult.messageId || bulkResult.messageId || '',
            errorMessage: recipientResult.errorMessage || bulkResult.errorMessage,
            errorCode: recipientResult.errorCode ? Number(recipientResult.errorCode) : undefined,
            provider: this.mapProviderTypeToHistoryProvider(providerType),
            cost: 0,
            isVietnameseNumber: this.isVietnamesePhoneNumber(this.getFullPhoneNumber(recipient)),
            brandName: bulkResult.brandName || 'REDAI',
          };

          smsHistories.push(historyData);
        }

        // Update job progress to 100%
        await job.updateProgress(100);

      } catch (error) {
        this.logger.error(`Error sending bulk SMS: ${error.message}`);

        // Tạo history cho tất cả recipients với status failed
        for (const recipient of jobData.recipients) {
          failedCount++;
          const historyData: SmsMarketingHistoryData = {
            campaignId: jobData.campaignId,
            recipientPhone: this.getFullPhoneNumber(recipient),
            recipientName: '',
            message: jobData.content || '',
            content: jobData.content || '',
            campaignType: String(jobData.campaignType),
            status: 'failed',
            sentAt,
            messageId: '',
            errorMessage: error.message,
            provider: providerType ? this.mapProviderTypeToHistoryProvider(providerType) : 'UNKNOWN',
            cost: 0,
            isVietnameseNumber: this.isVietnamesePhoneNumber(this.getFullPhoneNumber(recipient)),
            brandName: 'REDAI',
          };
          smsHistories.push(historyData);
        }
      }

      // Lưu lịch sử SMS marketing theo batch
      if (smsHistories.length > 0) {
        await this.smsMarketingService.batchSaveSmsMarketingHistory({
          histories: smsHistories,
          batchSize: 100,
        });
      }

      await this.updateCampaignAfterSend(jobData.campaignId, successCount, failedCount);

      this.logger.log(
        `SMS ADS job ${job.id} completed: ${successCount} sent, ${failedCount} failed`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing SMS ADS job ${job.id}: ${error.message}`,
        error.stack,
      );

      await this.smsMarketingService.updateCampaignStatus(
        jobData.campaignId,
        'FAILED',
        { completedAt: Date.now() },
      );

      throw error;
    }
  }

  /**
   * Helper function để chuẩn hóa số điện thoại theo tiêu chuẩn quốc tế
   * @param recipient Recipient object
   * @returns Full phone number theo format quốc tế
   */
  private getFullPhoneNumber(recipient: SmsRecipientDto): string {
    if (!recipient.phone) {
      throw new Error('Phone number is required');
    }

    // Loại bỏ tất cả ký tự không phải số từ phone
    const cleanPhone = recipient.phone.replace(/\D/g, '');

    if (!cleanPhone) {
      throw new Error('Invalid phone number format');
    }

    // Chuẩn hóa country code
    const normalizedCountryCode = this.normalizeCountryCode(recipient.countryCode);

    // Nếu không có countryCode hợp lệ
    if (!normalizedCountryCode) {
      return this.handlePhoneWithoutCountryCode(cleanPhone);
    }

    // Có countryCode hợp lệ
    return this.handlePhoneWithCountryCode(cleanPhone, normalizedCountryCode);
  }

  /**
   * Xử lý số điện thoại khi không có country code
   * @param cleanPhone Số điện thoại đã clean
   * @returns Formatted phone number (không có dấu +)
   */
  private handlePhoneWithoutCountryCode(cleanPhone: string): string {
    // Kiểm tra xem số đã có country code chưa
    if (this.isVietnamesePhoneNumber(cleanPhone)) {
      // Số Việt Nam - chuẩn hóa về format 84
      if (cleanPhone.startsWith('84')) {
        return cleanPhone;
      } else if (cleanPhone.startsWith('0')) {
        return `84${cleanPhone.substring(1)}`;
      } else {
        return `84${cleanPhone}`;
      }
    } else {
      // Số quốc tế khác hoặc số không xác định được
      if (cleanPhone.length >= 10 && cleanPhone.length <= 15) {
        // Số có độ dài hợp lệ cho quốc tế - giữ nguyên
        return cleanPhone;
      } else if (cleanPhone.length >= 8 && cleanPhone.length < 10) {
        // Số ngắn - mặc định là Việt Nam
        return `84${cleanPhone}`;
      } else {
        // Số quá ngắn hoặc quá dài - vẫn thử gửi
        return cleanPhone;
      }
    }
  }

  /**
   * Xử lý số điện thoại khi có country code
   * @param cleanPhone Số điện thoại đã clean
   * @param countryCode Country code đã chuẩn hóa
   * @returns Formatted phone number (không có dấu +)
   */
  private handlePhoneWithCountryCode(cleanPhone: string, countryCode: string): string {
    let finalPhone = cleanPhone;

    // Loại bỏ country code khỏi phone nếu phone đã chứa country code
    if (cleanPhone.startsWith(countryCode)) {
      finalPhone = cleanPhone.substring(countryCode.length);
    } else if (cleanPhone.startsWith('0') && countryCode === '84') {
      // Trường hợp đặc biệt cho Việt Nam: 0xxx -> xxx
      finalPhone = cleanPhone.substring(1);
    }

    // Đảm bảo finalPhone không rỗng
    if (!finalPhone) {
      throw new Error('Invalid phone number after processing');
    }

    return `${countryCode}${finalPhone}`;
  }

  /**
   * Test method để validate logic chuẩn hóa số điện thoại
   * Chỉ dùng cho development/testing
   */
  private testPhoneNormalization(): void {
    const testCases = [
      // Việt Nam với country code
      { phone: '0912345678', countryCode: 84, expected: '84912345678' },
      { phone: '912345678', countryCode: 84, expected: '84912345678' },
      { phone: '84912345678', countryCode: 84, expected: '84912345678' },

      // Việt Nam không có country code
      { phone: '0912345678', countryCode: null, expected: '84912345678' },
      { phone: '912345678', countryCode: null, expected: '84912345678' },
      { phone: '84912345678', countryCode: null, expected: '84912345678' },

      // Quốc tế
      { phone: '1234567890', countryCode: 1, expected: '11234567890' },
      { phone: '447123456789', countryCode: 44, expected: '447123456789' },

      // Edge cases
      { phone: '123456789', countryCode: null, expected: '84123456789' }, // Số ngắn -> VN
      { phone: '12345678901234', countryCode: null, expected: '12345678901234' }, // Số dài -> quốc tế
    ];

    this.logger.debug('=== Phone Normalization Test Cases ===');
    testCases.forEach((testCase, index) => {
      try {
        const result = this.getFullPhoneNumber({
          phone: testCase.phone,
          countryCode: testCase.countryCode as number
        });
        const status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';
        this.logger.debug(`Test ${index + 1}: ${status} - Input: ${testCase.phone} (${testCase.countryCode}) -> Output: ${result} (Expected: ${testCase.expected})`);
      } catch (error) {
        this.logger.debug(`Test ${index + 1}: ❌ ERROR - Input: ${testCase.phone} (${testCase.countryCode}) -> Error: ${error.message}`);
      }
    });
    this.logger.debug('=== End Test Cases ===');
  }

  /**
   * Helper function để convert SmsProviderTypeEnum sang SmsProvider cho history
   * @param providerType Provider type enum
   * @returns SmsProvider string
   */
  private mapProviderTypeToHistoryProvider(providerType: SmsProviderTypeEnum): string {
    switch (providerType) {
      case SmsProviderTypeEnum.SMS_FPT:
        return 'FPT_SMS';
      case SmsProviderTypeEnum.SMS_TWILIO:
        return 'TWILIO';
      case SmsProviderTypeEnum.SMS_ESMS_ADVERTISING:
        return 'ESMS_ADVERTISING';
      case SmsProviderTypeEnum.SMS_ESMS_CUSTOMER_CARE:
        return 'ESMS_CUSTOMER_CARE';
      default:
        return 'GENERIC';
    }
  }





  /**
   * Gửi bulk SMS ADS qua eSMS provider
   * @param recipients Danh sách recipients
   * @param content Nội dung SMS
   * @param serverId ID của integration
   * @param userId ID của user
   * @param campaignName Tên campaign
   * @returns Bulk SMS result
   */
  private async sendBulkEsmsAdsSms(
    recipients: SmsRecipientDto[],
    content: string,
    serverId: string,
    userId: number,
    campaignName: string,
  ): Promise<BulkSmsResult> {
    try {
      // Lấy eSMS integration
      const integration = await this.eSmsIntegrationRepository.findAndDecryptESmsIntegration(serverId, userId);
      if (!integration) {
        throw new Error(`eSMS integration not found: ${serverId}`);
      }

      const brandName = integration.integration.integrationName || 'REDAI';
      const phoneNumbers = recipients.map(r => this.getFullPhoneNumber(r));
      // Xác định provider type dựa trên serverId và userId
      // Vì không có direct relation, ta sẽ sử dụng default type cho eSMS ADS
      const providerType = SmsProviderTypeEnum.SMS_ESMS_ADVERTISING;

      this.logger.debug(`Sending bulk eSMS: provider=${providerType}, phones=${phoneNumbers.length}`);

      // Chỉ hỗ trợ SMS_ESMS_ADVERTISING cho ADS campaigns
      const smsType: '1' = '1'; // Type 1: brandname quảng cáo

      // Gửi từng SMS vì eSMS không có bulk API thực sự
      const results: Array<{ success: boolean; messageId?: string; errorMessage?: string }> = [];
      for (let i = 0; i < phoneNumbers.length; i++) {
        const phone = phoneNumbers[i];
        try {
          const smsPayload = {
            Phone: phone,
            Content: content,
            Brandname: brandName,
            SmsType: smsType,
            IsUnicode: '1' as '0' | '1',
            RequestId: `${campaignName}_${Date.now()}_${phone}`,
            SandBox: '0' as '0' | '1',
          };

          const result = await this.eSmsService.sms.sendMessageV4(smsPayload);

          if (result && (result.CodeResult === '100' || result.CodeResult === '100')) {
            results.push({
              success: true,
              messageId: result.SMSID || 'unknown',
            });
          } else {
            results.push({
              success: false,
              errorMessage: result?.ErrorMessage || `eSMS error code: ${result?.CodeResult}`,
            });
          }
        } catch (error: any) {
          results.push({
            success: false,
            errorMessage: error.message,
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failedCount = results.length - successCount;

      return {
        success: successCount > 0,
        results,
        brandName,
        totalSent: successCount,
        totalFailed: failedCount,
      };
    } catch (error) {
      this.logger.error(`Error sending bulk eSMS: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        brandName: 'REDAI',
        results: recipients.map(() => ({ success: false, errorMessage: error.message })),
      };
    }
  }

  /**
   * Gửi bulk SMS ADS qua FPT SMS provider
   * @param recipients Danh sách recipients
   * @param content Nội dung SMS
   * @param serverId ID của integration
   * @param userId ID của user
   * @param campaignName Tên campaign
   * @param scheduledAt Thời gian lên lịch
   * @returns Bulk SMS result
   */
  private async sendBulkFptAdsSms(
    recipients: SmsRecipientDto[],
    content: string,
    serverId: string,
    userId: number,
    campaignName?: string,
    scheduledAt?: number,
  ): Promise<BulkSmsResult> {
    try {
      // Lấy FPT SMS integration
      const integration = await this.fptSmsIntegrationRepository.findFptSmsIntegrationByIdWithDecryption(serverId, userId);
      if (!integration) {
        throw new Error(`FPT SMS integration not found: ${serverId}`);
      }

      const phoneNumbers = recipients.map(r => this.getFullPhoneNumber(r));
      const brandName = integration.brandName;

      // Kiểm tra tất cả số đều là số Việt Nam (FPT SMS ADS chỉ hỗ trợ trong nước)
      const nonVietnameseNumbers = phoneNumbers.filter(phone => !this.isVietnamesePhoneNumber(phone));
      if (nonVietnameseNumbers.length > 0) {
        this.logger.warn(`FPT SMS ADS không hỗ trợ số quốc tế: ${nonVietnameseNumbers.join(', ')}`);
      }

      if (!scheduledAt) {
        throw new Error('ADS campaign requires scheduledAt');
      }

      // Kiểm tra và mã hóa Base64 nếu cần
      let messageContent = content;
      if (!this.smsEncryptionService.isBase64Encoded(content)) {
        messageContent = this.smsEncryptionService.encryptSmsContent(content);
      }

      // Tính quota dựa trên độ dài message và số lượng recipients
      const quota = this.calculateQuota(messageContent, phoneNumbers.length);

      // Format ScheduleTime theo yêu cầu FPT: yyyy-mm-dd HH:ii
      const scheduleTime = new Date(scheduledAt * 1000)
        .toISOString()
        .slice(0, 16)
        .replace('T', ' ');

      // Tạo campaign
      const campaignPayload = {
        CampaignName: campaignName || `Campaign_${Date.now()}`,
        BrandName: brandName,
        Message: messageContent,
        ScheduleTime: scheduleTime,
        Quota: quota,
      };

      const campaignResult = await this.fptSmsBrandnameService.createCampaign(campaignPayload);

      // Gửi bulk ads
      const phoneList = phoneNumbers.join(',');
      const adsResult = await this.fptSmsBrandnameService.sendAds({
        CampaignCode: campaignResult.CampaignCode,
        PhoneList: phoneList,
      });

      // Xử lý kết quả
      const results = phoneNumbers.map(phone => {
        const detail = adsResult.Details?.find((d: any) => d.Phone === phone);
        if (detail) {
          return {
            success: !detail.ErrorMessage,
            messageId: detail.MessageId?.toString(),
            errorMessage: detail.ErrorMessage,
          };
        }
        return {
          success: adsResult.FailureCount === 0,
          errorMessage: adsResult.FailureCount > 0 ? 'Unknown error' : undefined,
        };
      });

      const successCount = results.filter(r => r.success).length;
      const failedCount = results.length - successCount;

      return {
        success: successCount > 0,
        results,
        brandName,
        totalSent: successCount,
        totalFailed: failedCount,
        campaignCode: campaignResult.CampaignCode,
      };
    } catch (error) {
      this.logger.error(`Error sending bulk FPT SMS: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        brandName: 'REDAI',
        results: recipients.map(() => ({ success: false, errorMessage: error.message })),
      };
    }
  }

  /**
   * Gửi bulk SMS ADS qua Twilio provider
   * @param recipients Danh sách recipients
   * @param content Nội dung SMS
   * @param serverId ID của integration
   * @param userId ID của user
   * @returns Bulk SMS result
   */
  private async sendBulkTwilioAdsSms(
    recipients: SmsRecipientDto[],
    content: string,
    serverId: string,
    userId: number,
  ): Promise<BulkSmsResult> {
    try {
      // Lấy Twilio SMS integration
      const integration = await this.twilioSmsIntegrationRepository.findByIdWithDecrypted(serverId, userId);
      if (!integration) {
        throw new Error(`Twilio SMS integration not found: ${serverId}`);
      }

      const config = integration.decryptedConfig;
      const brandName = integration.integration.integrationName || 'TWILIO';
      const phoneNumbers = recipients.map(r => this.getFullPhoneNumber(r));

      this.logger.debug(`Sending bulk Twilio SMS to ${phoneNumbers.length} numbers`);

      // Chuẩn bị Twilio options
      const twilioOptions: any = {
        accountSid: config.accountSid,
        authToken: config.authToken,
        providerType: 'TWILIO' as SmsProviderType,
      };

      // Thêm phone number hoặc messaging service SID
      if (config.messagingServiceSid) {
        twilioOptions.messagingServiceSid = config.messagingServiceSid;
      } else if (config.fromPhone) {
        twilioOptions.from = config.fromPhone;
      }

      // Gửi từng SMS vì Twilio không có bulk API miễn phí
      const results: Array<{ success: boolean; messageId?: string; errorMessage?: string }> = [];
      for (const phone of phoneNumbers) {
        try {
          const result = await this.smsService.sendSms(phone, content, twilioOptions);
          results.push({
            success: result.success,
            messageId: result.messageId,
            errorMessage: result.errorMessage,
          });
        } catch (error: any) {
          results.push({
            success: false,
            errorMessage: error.message,
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failedCount = results.length - successCount;

      return {
        success: successCount > 0,
        results,
        brandName,
        totalSent: successCount,
        totalFailed: failedCount,
      };
    } catch (error) {
      this.logger.error(`Error sending bulk Twilio SMS: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        brandName: 'TWILIO',
        results: recipients.map(() => ({ success: false, errorMessage: error.message })),
      };
    }
  }

  /**
   * Validate OTP job data
   * @param jobData Dữ liệu job OTP
   * @returns True nếu valid
   */
  private validateOtpJobData(jobData: SmsOtpJobDto): boolean {
    if (!jobData.campaignId) {
      this.logger.error('Missing campaignId');
      return false;
    }

    if (!jobData.content || jobData.content.trim() === '') {
      this.logger.error('Missing or empty content');
      return false;
    }

    if (!jobData.recipient || !jobData.recipient.phone || !jobData.recipient.countryCode) {
      this.logger.error('Missing recipient, phone, or countryCode');
      return false;
    }

    if (!jobData.serverId) {
      this.logger.error('Missing serverId');
      return false;
    }

    const fullPhone = this.getFullPhoneNumber(jobData.recipient);
    if (!this.isValidPhone(fullPhone)) {
      this.logger.error(`Invalid phone: ${fullPhone}`);
      return false;
    }

    return true;
  }

  /**
   * Validate ADS job data
   * @param jobData Dữ liệu job ADS
   * @returns True nếu valid
   */
  private validateAdsJobData(jobData: SmsAdsJobDto): boolean {
    if (!jobData.campaignId) {
      this.logger.error('Missing campaignId');
      return false;
    }

    if (!jobData.content || jobData.content.trim() === '') {
      this.logger.error('Missing or empty content');
      return false;
    }

    if (!jobData.recipients || !Array.isArray(jobData.recipients) || jobData.recipients.length === 0) {
      this.logger.error('Missing or empty recipients array');
      return false;
    }

    if (!jobData.serverId) {
      this.logger.error('Missing serverId');
      return false;
    }

    if (!jobData.scheduledAt) {
      this.logger.error('Missing scheduledAt for ADS campaign');
      return false;
    }

    // Validate từng recipient
    for (const recipient of jobData.recipients) {
      if (!recipient.phone || !recipient.countryCode) {
        this.logger.error(`Missing phone or countryCode for recipient`);
        return false;
      }
      const fullPhone = this.getFullPhoneNumber(recipient);
      if (!this.isValidPhone(fullPhone)) {
        this.logger.error(`Invalid phone: ${fullPhone}`);
        return false;
      }
    }

    return true;
  }



  /**
   * Gửi SMS OTP qua FPT SMS provider
   */
  private async sendFptOtpSms(
    phone: string,
    content: string,
    serverId: string,
    userId: number,
    campaignName?: string,
    scheduledAt?: number,
  ): Promise<SmsSendResult> {
    try {
      // Lấy FPT SMS integration
      const integration = await this.fptSmsIntegrationRepository.findFptSmsIntegrationByIdWithDecryption(serverId, userId);
      if (!integration) {
        throw new Error(`FPT SMS integration not found: ${serverId}`);
      }

      const isVietnameseNumber = this.isVietnamesePhoneNumber(phone);

      if (isVietnameseNumber) {
        // Kiểm tra và mã hóa Base64 nếu cần cho SMS OTP trong nước
        let messageContent = content;
        if (!this.smsEncryptionService.isBase64Encoded(content)) {
          messageContent = this.smsEncryptionService.encryptSmsContent(content);
        }

        // Gửi SMS OTP trong nước
        const result = await this.fptSmsBrandnameService.sendOtp({
          BrandName: integration.brandName,
          Phone: phone,
          Message: messageContent,
        });

        if (result.error && result.error !== 0) {
          return {
            success: false,
            errorMessage: result.error_description || 'Unknown error',
            errorCode: result.error,
            isVietnameseNumber,
            brandName: integration.brandName,
          };
        }

        return {
          success: true,
          messageId: result.MessageId?.toString(),
          brandName: integration.brandName,
          partnerId: result.PartnerId,
          telco: result.Telco,
          isVietnameseNumber,
        };
      } else {
        // Gửi SMS OTP quốc tế
        const result = await this.fptSmsBrandnameService.sendInternationalSms(
          phone,
          content,
          {
            brandName: integration.brandName,
            requestId: `sms_otp_${Date.now()}`,
          },
        );

        if (!result.success) {
          return {
            success: false,
            errorMessage: result.errorMessage || 'Unknown error',
            isVietnameseNumber,
            brandName: integration.brandName,
          };
        }

        return {
          success: true,
          messageId: result.messageId?.toString(),
          brandName: integration.brandName,
          partnerId: result.rawResponse?.PartnerId,
          telco: result.rawResponse?.Telco,
          isVietnameseNumber,
        };
      }
    } catch (error) {
      this.logger.error(`Error sending FPT OTP SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber: this.isVietnamesePhoneNumber(phone),
        brandName: 'REDAI',
      };
    }
  }

  /**
   * Gửi SMS ADS qua FPT SMS provider (legacy method)
   */
  private async sendFptSmsByIntegration(
    phone: string,
    content: string,
    campaignType: SmsCampaignType,
    serverId: string,
    userId: number,
    campaignName?: string,
    isVietnameseNumber: boolean = false,
    scheduledAt?: number,
  ): Promise<SmsSendResult> {
    try {
      // Lấy FPT SMS integration
      const integration = await this.fptSmsIntegrationRepository.findFptSmsIntegrationByIdWithDecryption(serverId, userId);
      if (!integration) {
        throw new Error(`FPT SMS integration not found: ${serverId}`);
      }

      if (campaignType === SmsCampaignType.OTP) {
        if (isVietnameseNumber) {
          // Kiểm tra và mã hóa Base64 nếu cần cho SMS OTP trong nước
          let messageContent = content;
          if (!this.smsEncryptionService.isBase64Encoded(content)) {
            messageContent = this.smsEncryptionService.encryptSmsContent(content);
          }

          // Gửi SMS OTP trong nước
          const result = await this.fptSmsBrandnameService.sendOtp({
            BrandName: integration.brandName,
            Phone: phone,
            Message: messageContent,
          });

          if (result.error && result.error !== 0) {
            return {
              success: false,
              errorMessage: result.error_description || 'Unknown error',
              errorCode: result.error,
              isVietnameseNumber,
              brandName: integration.brandName,
            };
          }

          return {
            success: true,
            messageId: result.MessageId?.toString(),
            brandName: integration.brandName,
            partnerId: result.PartnerId,
            telco: result.Telco,
            isVietnameseNumber,
          };
        } else {
          // Gửi SMS OTP quốc tế
          const result = await this.fptSmsBrandnameService.sendInternationalSms(
            phone,
            content,
            {
              brandName: integration.brandName,
              requestId: `sms_marketing_${Date.now()}`,
            },
          );

          if (!result.success) {
            return {
              success: false,
              errorMessage: result.errorMessage || 'Unknown error',
              isVietnameseNumber,
              brandName: integration.brandName,
            };
          }

          return {
            success: true,
            messageId: result.messageId?.toString(),
            brandName: integration.brandName,
            partnerId: result.rawResponse?.PartnerId,
            telco: result.rawResponse?.Telco,
            isVietnameseNumber,
          };
        }
      } else {
        // Gửi SMS campaign ads (brandname) - chỉ hỗ trợ trong nước
        if (!isVietnameseNumber) {
          return {
            success: false,
            errorMessage: 'Campaign ads SMS chỉ hỗ trợ số điện thoại Việt Nam',
            isVietnameseNumber,
            brandName: integration.brandName,
          };
        }

        if (!scheduledAt) {
          throw new Error('ADS campaign requires scheduledAt');
        }

        // Kiểm tra và mã hóa Base64 nếu cần
        let messageContent = content;
        if (!this.smsEncryptionService.isBase64Encoded(content)) {
          messageContent = this.smsEncryptionService.encryptSmsContent(content);
        }

        // Tính quota dựa trên độ dài message
        const quota = this.calculateQuota(messageContent, 1);

        // Format ScheduleTime theo yêu cầu FPT: yyyy-mm-dd HH:ii
        const scheduleTime = new Date(scheduledAt * 1000)
          .toISOString()
          .slice(0, 16)
          .replace('T', ' ');

        const campaignPayload = {
          CampaignName: campaignName || `Campaign_${Date.now()}`,
          BrandName: integration.brandName,
          Message: messageContent,
          ScheduleTime: scheduleTime,
          Quota: quota,
        };

        const campaignResult = await this.fptSmsBrandnameService.createCampaign(campaignPayload);

        // Gửi ads
        const adsResult = await this.fptSmsBrandnameService.sendAds({
          CampaignCode: campaignResult.CampaignCode,
          PhoneList: phone,
        });

        if (adsResult.FailureCount > 0) {
          const failedDetail = adsResult.Details?.find((detail) => detail.Phone === phone);
          return {
            success: false,
            errorMessage: failedDetail?.ErrorMessage || 'Unknown error',
            isVietnameseNumber,
            brandName: integration.brandName,
          };
        }

        const successDetail = adsResult.Details.find((detail) => detail.Phone === phone);

        return {
          success: true,
          messageId: successDetail?.MessageId?.toString(),
          brandName: integration.brandName,
          isVietnameseNumber,
        };
      }
    } catch (error) {
      this.logger.error(`Error sending FPT SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber,
        brandName: 'REDAI',
      };
    }
  }

  /**
   * Gửi SMS OTP qua eSMS provider
   */
  private async sendEsmsOtpSms(
    phone: string,
    content: string,
    serverId: string,
    userId: number,
    campaignName: string,
  ): Promise<SmsSendResult> {
    try {
      // Lấy eSMS integration
      const integration = await this.eSmsIntegrationRepository.findAndDecryptESmsIntegration(serverId, userId);
      if (!integration) {
        throw new Error(`eSMS integration not found: ${serverId}`);
      }

      const brandName = integration.integration.integrationName || 'REDAI';
      const isVietnameseNumber = this.isVietnamesePhoneNumber(phone);
      // OTP chỉ sử dụng SMS_ESMS_CUSTOMER_CARE (CSKH)
      const providerType = SmsProviderTypeEnum.SMS_ESMS_CUSTOMER_CARE;

      const smsType: '2' = '2'; // Type 2: brandname CSKH cho OTP

      // Chuẩn bị payload cho eSMS
      const smsPayload = {
        Phone: phone,
        Content: content,
        Brandname: brandName,
        SmsType: smsType,
        IsUnicode: '1' as '0' | '1',
        RequestId: `${campaignName}_otp_${Date.now()}_${phone}`,
        SandBox: '0' as '0' | '1',
      };

      // Gửi SMS OTP
      const result = await this.eSmsService.sms.sendMessageV4(smsPayload);

      // Xử lý kết quả
      if (result && (result.CodeResult === '100')) {
        this.logger.debug(`eSMS OTP SMS sent successfully: ${result.SMSID || result.SMSID}`);
        return {
          success: true,
          messageId: result.SMSID || result.SMSID || 'unknown',
          brandName,
          isVietnameseNumber,
        };
      } else {
        const errorMessage = result?.ErrorMessage || `eSMS error code: ${result?.CodeResult}`;
        this.logger.error(`eSMS OTP SMS failed: ${errorMessage}`);
        return {
          success: false,
          errorMessage,
          isVietnameseNumber,
          brandName,
        };
      }
    } catch (error) {
      this.logger.error(`Error sending eSMS OTP SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber: this.isVietnamesePhoneNumber(phone),
        brandName: 'REDAI',
      };
    }
  }

  /**
   * Gửi SMS ADS qua eSMS provider (legacy method)
   */
  private async sendEsmsSmsByIntegration(
    phone: string,
    content: string,
    _campaignType: SmsCampaignType,
    serverId: string,
    userId: number,
    campaignName: string,
    isVietnameseNumber: boolean = false,
  ): Promise<SmsSendResult> {
    try {
      // Lấy eSMS integration
      const integration = await this.eSmsIntegrationRepository.findAndDecryptESmsIntegration(serverId, userId);
      if (!integration) {
        throw new Error(`eSMS integration not found: ${serverId}`);
      }

      const brandName = integration.integration.integrationName || 'REDAI';

      // Xác định SmsType dựa trên integration provider type
      let smsType: '1' | '2' | '8';
      // Xác định SmsType dựa trên campaign type
      // Vì đây là legacy method, ta sẽ sử dụng default logic
      const providerType = SmsProviderTypeEnum.SMS_ESMS_CUSTOMER_CARE;
      smsType = '2'; // Type 2: brandname CSKH (default cho legacy method)

      this.logger.debug(
        `Sending eSMS SMS: provider=${providerType}, smsType=${smsType}, phone=${phone}`,
      );

      // Chuẩn bị payload cho eSMS
      const smsPayload = {
        Phone: phone,
        Content: content,
        Brandname: brandName,
        SmsType: smsType,
        IsUnicode: '1' as '0' | '1',
        RequestId: `${campaignName}_${Date.now()}_${phone}`,
        SandBox: '0' as '0' | '1',
      };

      // Gửi SMS với SmsType đã được xác định
      const result = await this.eSmsService.sms.sendMessageV4(smsPayload);

      // Xử lý kết quả
      if (result && result.CodeResult === '100') {
        this.logger.debug(
          `eSMS SMS sent successfully: ${result.SMSID}`,
        );
        return {
          success: true,
          messageId: result.SMSID || 'unknown',
          brandName,
          isVietnameseNumber,
        };
      } else {
        const errorMessage = result?.ErrorMessage || `eSMS error code: ${result?.CodeResult}`;
        this.logger.error(`eSMS SMS failed: ${errorMessage}`);
        return {
          success: false,
          errorMessage,
          isVietnameseNumber,
          brandName,
        };
      }
    } catch (error) {
      this.logger.error(`Error sending eSMS SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber,
        brandName: 'REDAI',
      };
    }
  }

  /**
   * Gửi SMS OTP qua Twilio provider
   */
  private async sendTwilioOtpSms(
    phone: string,
    content: string,
    serverId: string,
    userId: number,
  ): Promise<SmsSendResult> {
    try {
      // Lấy Twilio SMS integration
      const integration = await this.twilioSmsIntegrationRepository.findByIdWithDecrypted(serverId, userId);
      if (!integration) {
        throw new Error(`Twilio SMS integration not found: ${serverId}`);
      }

      const config = integration.decryptedConfig;
      const isVietnameseNumber = this.isVietnamesePhoneNumber(phone);

      // Gửi SMS OTP qua Twilio
      const twilioOptions: any = {
        accountSid: config.accountSid,
        authToken: config.authToken,
        providerType: 'TWILIO' as SmsProviderType,
      };

      // Thêm phone number hoặc messaging service SID
      if (config.messagingServiceSid) {
        twilioOptions.messagingServiceSid = config.messagingServiceSid;
      } else if (config.fromPhone) {
        twilioOptions.from = config.fromPhone;
      }

      const result = await this.smsService.sendSms(phone, content, twilioOptions);

      if (result.success) {
        return {
          success: true,
          messageId: result.messageId,
          brandName: integration.integration.integrationName || 'TWILIO',
          isVietnameseNumber,
        };
      } else {
        return {
          success: false,
          errorMessage: result.errorMessage || 'Unknown Twilio error',
          isVietnameseNumber,
          brandName: integration.integration.integrationName || 'TWILIO',
        };
      }
    } catch (error) {
      this.logger.error(`Error sending Twilio OTP SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber: this.isVietnamesePhoneNumber(phone),
        brandName: 'TWILIO',
      };
    }
  }

  /**
   * Gửi SMS ADS qua Twilio provider (legacy method)
   */
  private async sendTwilioSmsByIntegration(
    phone: string,
    content: string,
    _campaignType: SmsCampaignType,
    serverId: string,
    userId: number,
    isVietnameseNumber: boolean = false,
  ): Promise<SmsSendResult> {
    try {
      // Lấy Twilio SMS integration
      const integration = await this.twilioSmsIntegrationRepository.findByIdWithDecrypted(serverId, userId);
      if (!integration) {
        throw new Error(`Twilio SMS integration not found: ${serverId}`);
      }

      const config = integration.decryptedConfig;
      const brandName = integration.integration.integrationName || 'TWILIO';

      // Chuẩn bị Twilio options
      const twilioOptions: any = {
        accountSid: config.accountSid,
        authToken: config.authToken,
        providerType: 'TWILIO' as SmsProviderType,
      };

      // Thêm phone number hoặc messaging service SID
      if (config.messagingServiceSid) {
        twilioOptions.messagingServiceSid = config.messagingServiceSid;
      } else if (config.fromPhone) {
        twilioOptions.from = config.fromPhone;
      }

      // Gửi SMS qua Twilio provider
      const result = await this.smsService.sendSms(phone, content, twilioOptions);

      if (result.success) {
        this.logger.debug(`Twilio SMS sent successfully: ${result.messageId}`);
        return {
          success: true,
          messageId: result.messageId,
          brandName,
          isVietnameseNumber,
        };
      } else {
        return {
          success: false,
          errorMessage: result.errorMessage || 'Unknown Twilio error',
          isVietnameseNumber,
          brandName,
        };
      }
    } catch (error) {
      this.logger.error(`Error sending Twilio SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber,
        brandName: 'TWILIO',
      };
    }
  }

  /**
   * Lưu lịch sử SMS cho single recipient
   */
  private async saveSingleSmsHistory(jobData: SmsOtpJobDto, sendResult: SmsSendResult, providerType?: SmsProviderTypeEnum): Promise<void> {
    const historyData: SmsMarketingHistoryData = {
      campaignId: jobData.campaignId,
      recipientPhone: this.getFullPhoneNumber(jobData.recipient),
      recipientName: '',
      message: jobData.content || '',
      content: jobData.content || '',
      campaignType: String(jobData.campaignType),
      status: sendResult.success ? 'sent' : 'failed',
      sentAt: Date.now(),
      messageId: sendResult.messageId || '',
      errorMessage: sendResult.errorMessage,
      errorCode: sendResult.errorCode ? Number(sendResult.errorCode) : undefined,
      provider: providerType ? this.mapProviderTypeToHistoryProvider(providerType) : 'GENERIC',
      cost: 0,
      isVietnameseNumber: this.isVietnamesePhoneNumber(this.getFullPhoneNumber(jobData.recipient)),
      brandName: sendResult.brandName || 'REDAI',
    };

    await this.smsMarketingService.batchSaveSmsMarketingHistory({
      histories: [historyData],
      batchSize: 1,
    });
  }

  /**
   * Cập nhật campaign sau khi gửi SMS
   */
  private async updateCampaignAfterSend(campaignId: number, successCount: number, failedCount: number): Promise<void> {
    await this.smsMarketingService.updateCampaignCounts(campaignId, successCount, failedCount);

    const finalStatus = failedCount === 0 ? 'SENT' : successCount === 0 ? 'FAILED' : 'SENT';
    await this.smsMarketingService.updateCampaignStatus(campaignId, finalStatus, { completedAt: Date.now() });
  }

  /**
   * Tính quota cho campaign dựa trên độ dài message và số lượng recipients
   * @param messageContent Nội dung tin nhắn
   * @param recipientCount Số lượng người nhận
   * @returns Quota cần thiết
   */
  private calculateQuota(messageContent: string, recipientCount: number): number {
    const maxCharsPerSms = 160; // Giả sử sử dụng ký tự Latin
    const messageLength = messageContent.length;
    const smsCountPerMessage = Math.ceil(messageLength / maxCharsPerSms);
    const totalQuota = smsCountPerMessage * recipientCount;

    this.logger.debug(
      `Calculated quota: messageLength=${messageLength}, smsPerMessage=${smsCountPerMessage}, recipients=${recipientCount}, totalQuota=${totalQuota}`,
    );

    return totalQuota;
  }

  /**
   * Kiểm tra số điện thoại có phải là số Việt Nam hay không
   * @param phone Số điện thoại cần kiểm tra
   * @returns true nếu là số Việt Nam, false nếu là số quốc tế
   */
  private isVietnamesePhoneNumber(phone: string): boolean {
    const cleanNumber = phone.replace(/\D/g, '');

    const vietnamesePatterns = {
      mobile: /^(84)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/,
      mobileWithZero: /^(0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/,
      landline: /^(84)(2[0-9])\d{7,8}$/,
      landlineWithZero: /^(0)(2[0-9])\d{7,8}$/,
    };

    return (
      vietnamesePatterns.mobile.test(cleanNumber) ||
      vietnamesePatterns.mobileWithZero.test(cleanNumber) ||
      vietnamesePatterns.landline.test(cleanNumber) ||
      vietnamesePatterns.landlineWithZero.test(cleanNumber)
    );
  }

  /**
   * Validate phone number format (cả trong nước và quốc tế)
   * @param phone Phone number cần validate
   * @returns True nếu phone hợp lệ
   */
  private isValidPhone(phone: string): boolean {
    // Loại bỏ spaces (không cần loại bỏ dấu + vì không sử dụng)
    const cleanPhone = phone.replace(/\s/g, '');

    // Kiểm tra format cơ bản: chỉ chứa số và có độ dài hợp lệ
    if (!/^\d+$/.test(cleanPhone) || cleanPhone.length < 8 || cleanPhone.length > 15) {
      return false;
    }

    // Kiểm tra số Việt Nam
    if (this.isVietnamesePhoneNumber(phone)) {
      return true;
    }

    // Kiểm tra số quốc tế khác (độ dài từ 8-15 số)
    return cleanPhone.length >= 8 && cleanPhone.length <= 15;
  }

  /**
   * Kiểm tra và chuẩn hóa country code
   * @param countryCode Country code cần kiểm tra
   * @returns Country code đã chuẩn hóa hoặc null nếu không hợp lệ
   */
  private normalizeCountryCode(countryCode: number | string | null | undefined): string | null {
    if (!countryCode) return null;

    const code = countryCode.toString().replace(/\D/g, '');

    // Kiểm tra country code hợp lệ (1-4 chữ số)
    if (code.length >= 1 && code.length <= 4) {
      return code;
    }

    return null;
  }

  /**
   * Xử lý khi job failed
   * @param job Job bị failed
   * @param err Lỗi
   */
  async onFailed(job: Job<SmsOtpJobDto | SmsAdsJobDto>, err: Error): Promise<void> {
    if (job.name !== SmsJobName.SMS_MARKETING_OTP && job.name !== SmsJobName.SMS_MARKETING_ADS) {
      return;
    }

    this.logger.error(`SMS marketing job ${job.id} failed: ${err.message}`, err.stack);

    const jobData = job.data;

    try {
      await this.smsMarketingService.updateCampaignStatus(
        jobData.campaignId,
        'FAILED',
        { completedAt: Date.now() },
      );
    } catch (updateError) {
      this.logger.error(`Error updating campaign status on job failure: ${updateError.message}`);
    }
  }

  /**
   * Xử lý khi job completed
   * @param job Job đã hoàn thành
   */
  async onCompleted(job: Job<SmsOtpJobDto | SmsAdsJobDto>): Promise<void> {
    if (job.name !== SmsJobName.SMS_MARKETING_OTP && job.name !== SmsJobName.SMS_MARKETING_ADS) {
      return;
    }

    const jobData = job.data;
    this.logger.debug(`SMS marketing job ${job.id} completed for campaign ${jobData.campaignId}`);
  }

  /**
   * Xử lý khi job active
   * @param job Job đang xử lý
   */
  async onActive(job: Job<SmsOtpJobDto | SmsAdsJobDto>): Promise<void> {
    if (job.name !== SmsJobName.SMS_MARKETING_OTP && job.name !== SmsJobName.SMS_MARKETING_ADS) {
      return;
    }

    const jobData = job.data;
    this.logger.debug(`SMS marketing job ${job.id} started for campaign ${jobData.campaignId}`);
  }
}
