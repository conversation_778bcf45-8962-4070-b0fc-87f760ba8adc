import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException, ErrorCode } from '@common/exceptions';
import { KeyPairEncryptionService } from '../services/encryption/key-pair-encryption.service';
import { Integration, IntegrationProvider } from '../entities';
import { FptSmsMetadata } from '../interfaces';
import { IntegrationProviderRepository } from './integration-provider.repository';

/**
 * Interface cho dữ liệu mã hóa FPT SMS
 */
interface FptSmsEncryptedConfig {
  FPT_SMS_CLIENT_ID: string;
  FPT_SMS_CLIENT_SECRET: string;
}

/**
 * Repository chuyên biệt cho FPT SMS Integration
 * Extend từ Repository<Integration> để có đầy đủ các method cơ bản
 */
@Injectable()
export class FptSmsIntegrationRepository extends Repository<Integration> {
  private readonly logger = new Logger(FptSmsIntegrationRepository.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
    private readonly integrationProviderRepository: IntegrationProviderRepository,
  ) {
    super(
      integrationRepository.target,
      integrationRepository.manager,
      integrationRepository.queryRunner,
    );
  }

  /**
   * Lấy IntegrationProvider cho SMS_FPT
   */
  private async getFptSmsProvider(): Promise<IntegrationProvider> {
    const provider = await this.integrationProviderRepository.findByType('SMS_FPT');
    if (!provider) {
      throw new AppException(
        ErrorCode.BAD_REQUEST,
        'IntegrationProvider với type SMS_FPT không tồn tại',
      );
    }
    return provider;
  }



  /**
   * Lấy FPT SMS integration theo ID
   */
  async findFptSmsIntegrationById(id: string, userId: number): Promise<Integration | null> {
    try {
      const provider = await this.getFptSmsProvider();

      const integration = await this.findOne({
        where: {
          id,
          typeId: provider.id,
          userId,
        },
      });

      if (integration) {
        // Manually attach provider
        (integration as any).provider = provider;
      }

      return integration;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy FPT SMS integration: ${error.message}`, error.stack);
      throw error;
    }
  }



  /**
   * Xóa FPT SMS integration
   */
  async deleteFptSmsIntegration(id: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa FPT SMS integration ${id} cho user ${userId}`);

      const existingIntegration = await this.findFptSmsIntegrationById(id, userId);
      if (!existingIntegration) {
        return false;
      }

      const result = await this.delete(id);
      const deleted = (result.affected ?? 0) > 0;

      if (deleted) {
        this.logger.log(`Đã xóa FPT SMS integration ${id}`);
      }

      return deleted;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa FPT SMS integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy cấu hình FPT SMS đầy đủ để test kết nối (bao gồm dữ liệu đã giải mã)
   */
  async getFptSmsConfigForTesting(
    id: string,
    userId: number,
  ): Promise<{
    clientId: string;
    clientSecret: string;
    brandName: string;
    endpoint: string;
  } | null> {
    try {
      this.logger.log(`Lấy cấu hình FPT SMS để test ${id} cho user ${userId}`);

      const integration = await this.findFptSmsIntegrationById(id, userId);
      if (!integration) {
        return null;
      }

      // Giải mã encryptedConfig
      let decryptedConfig: FptSmsEncryptedConfig = {
        FPT_SMS_CLIENT_ID: '',
        FPT_SMS_CLIENT_SECRET: '',
      };

      if (integration.encryptedConfig && integration.secretKey) {
        try {
          const decryptionResult = this.keyPairEncryptionService.decrypt(
            integration.encryptedConfig,
            integration.secretKey,
          );

          if (decryptionResult.success) {
            decryptedConfig = JSON.parse(decryptionResult.decryptedData);
          }
        } catch (error) {
          this.logger.warn(
            `Không thể giải mã encryptedConfig cho integration ${id}: ${error.message}`,
          );
        }
      }

      const metadata = integration.metadata as unknown as FptSmsMetadata;

      return {
        clientId: decryptedConfig.FPT_SMS_CLIENT_ID,
        clientSecret: decryptedConfig.FPT_SMS_CLIENT_SECRET,
        brandName: metadata?.brandName || '',
        endpoint: metadata?.apiUrl || 'https://api01.sms.fpt.net',
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình FPT SMS để test: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy FPT SMS integration theo ID và trả về kèm dữ liệu đã giải mã
   */
  async findFptSmsIntegrationByIdWithDecryption(
    id: string,
    userId: number,
  ): Promise<
    | {
        integration: Integration;
        decrypted: { clientId: string; clientSecret: string };
        brandName: string;
        endpoint: string;
      }
    | null
  > {
    try {
      this.logger.log(`Lấy FPT SMS integration (kèm giải mã) id=${id} cho user ${userId}`);

      const integration = await this.findFptSmsIntegrationById(id, userId);
      if (!integration) return null;

      // Giải mã cấu hình nhạy cảm
      let clientId = '';
      let clientSecret = '';
      if (integration.encryptedConfig && integration.secretKey) {
        try {
          const decryptionResult = this.keyPairEncryptionService.decrypt(
            integration.encryptedConfig,
            integration.secretKey,
          );
          if (decryptionResult.success) {
            const cfg = JSON.parse(decryptionResult.decryptedData) as FptSmsEncryptedConfig;
            clientId = cfg.FPT_SMS_CLIENT_ID;
            clientSecret = cfg.FPT_SMS_CLIENT_SECRET;
          }
        } catch (error: any) {
          this.logger.warn(
            `Không thể giải mã encryptedConfig cho integration ${id}: ${error.message}`,
          );
        }
      }

      const metadata = integration.metadata as unknown as FptSmsMetadata;

      return {
        integration,
        decrypted: { clientId, clientSecret },
        brandName: metadata?.brandName || '',
        endpoint: metadata?.apiUrl || 'https://api01.sms.fpt.net',
      };
    } catch (error: any) {
      this.logger.error(
        `Lỗi khi lấy FPT SMS integration kèm giải mã: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
