import { AppException, ErrorCode } from '@common/exceptions';
import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { JobsOptions, Queue } from 'bullmq';
import { PageNavigationJobName } from './job-names.enum';
import { QueueName } from './queue-name.enum';
import { PageNavigationJobData } from './queue.types';

/**
 * DTO cho page navigation job
 */
export interface PageNavigationJobDto {
  /**
   * Đường dẫn trang cần chuyển đến
   */
  path: string;

  /**
   * ID người dùng (optional - nếu có thì chỉ gửi cho user cụ thể)
   */
  userId?: number;

  /**
   * Dữ liệu bổ sung để gửi kèm
   */
  data?: Record<string, any>;

  /**
   * Loại navigation
   */
  type?: 'navigate' | 'redirect' | 'replace';

  /**
   * Thông báo kèm theo
   */
  message?: string;

  /**
   * Độ <PERSON>u tiên (optional)
   */
  priority?: 'low' | 'normal' | 'high';
}

/**
 * Service quản lý việc thêm job vào PAGE_NAVIGATION queue
 */
@Injectable()
export class PageNavigationQueueService {
  private readonly logger = new Logger(PageNavigationQueueService.name);

  constructor(
    @InjectQueue(QueueName.PAGE_NAVIGATION)
    private readonly pageNavigationQueue: Queue,
  ) {}

  /**
   * Thêm job navigate to page vào queue
   * @param jobData Dữ liệu job
   * @param options Tùy chọn job (optional)
   * @returns Promise với ID của job đã tạo
   */
  async addNavigateToPageJob(
    jobData: PageNavigationJobDto,
    options?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      // Validate dữ liệu đầu vào
      this.validateJobData(jobData);

      // Tạo job data với timestamp
      const pageNavigationJobData: PageNavigationJobData = {
        ...jobData,
        timestamp: Date.now(),
        trackingId: `nav-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      // Xác định priority cho job options
      const jobOptions: JobsOptions = {
        ...options,
        priority: this.getPriorityValue(jobData.priority || 'normal'),
      };

      const job = await this.pageNavigationQueue.add(
        PageNavigationJobName.NAVIGATE_TO_PAGE,
        pageNavigationJobData,
        jobOptions,
      );

      this.logger.log(`Đã thêm job page navigation vào queue: ${job.id} - Path: ${jobData.path}`);

      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job page navigation vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job page navigation vào queue',
      );
    }
  }

  /**
   * Thêm job page status update vào queue
   * @param jobData Dữ liệu job
   * @param options Tùy chọn job (optional)
   * @returns Promise với ID của job đã tạo
   */
  async addPageStatusUpdateJob(
    jobData: PageNavigationJobDto,
    options?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      // Validate dữ liệu đầu vào
      this.validateJobData(jobData);

      // Tạo job data với timestamp
      const pageNavigationJobData: PageNavigationJobData = {
        ...jobData,
        timestamp: Date.now(),
        trackingId: `status-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      const job = await this.pageNavigationQueue.add(
        PageNavigationJobName.PAGE_STATUS_UPDATE,
        pageNavigationJobData,
        options,
      );

      this.logger.log(
        `Đã thêm job page status update vào queue: ${job.id} - Path: ${jobData.path}`,
      );

      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job page status update vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job page status update vào queue',
      );
    }
  }

  /**
   * Lấy thống kê queue
   * @returns Thống kê queue
   */
  async getQueueStats() {
    try {
      const waiting = await this.pageNavigationQueue.getWaiting();
      const active = await this.pageNavigationQueue.getActive();
      const completed = await this.pageNavigationQueue.getCompleted();
      const failed = await this.pageNavigationQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total: waiting.length + active.length + completed.length + failed.length,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê queue: ${error.message}`);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lấy thống kê queue');
    }
  }

  /**
   * Dọn dẹp queue (xóa các job đã hoàn thành và thất bại)
   * @returns Promise<void>
   */
  async cleanQueue(): Promise<void> {
    try {
      await this.pageNavigationQueue.clean(0, 100, 'completed');
      await this.pageNavigationQueue.clean(0, 100, 'failed');
      this.logger.log('Đã dọn dẹp page navigation queue');
    } catch (error) {
      this.logger.error(`Lỗi khi dọn dẹp queue: ${error.message}`);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể dọn dẹp queue');
    }
  }

  /**
   * Validate dữ liệu job
   * @param jobData Dữ liệu job cần validate
   */
  private validateJobData(jobData: PageNavigationJobDto): void {
    if (!jobData.path || typeof jobData.path !== 'string') {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Path là bắt buộc và phải là string');
    }

    if (jobData.userId && typeof jobData.userId !== 'number') {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'UserId phải là number nếu được cung cấp');
    }

    if (jobData.type && !['navigate', 'redirect', 'replace'].includes(jobData.type)) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Type phải là một trong: navigate, redirect, replace',
      );
    }

    if (jobData.priority && !['low', 'normal', 'high'].includes(jobData.priority)) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Priority phải là một trong: low, normal, high',
      );
    }
  }

  /**
   * Chuyển đổi priority string thành số
   * @param priority Priority string
   * @returns Priority number
   */
  private getPriorityValue(priority: 'low' | 'normal' | 'high'): number {
    switch (priority) {
      case 'high':
        return 10;
      case 'normal':
        return 5;
      case 'low':
        return 1;
      default:
        return 5;
    }
  }
}