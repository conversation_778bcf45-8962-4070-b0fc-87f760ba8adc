import {
  ContactMessageDto,
  LocationMessageDto,
  MessageAttachmentDto,
  MessageType,
  QuickReplyButtonDto,
  TemplateMessageDto,
} from '../dto/message.dto';

/**
 * Base interface cho tất cả message data
 */
export interface BaseMessageData {
  /** Loại tin nhắn (text, image, file, etc.) */
  type: MessageType;
  /** Thời gian tạo tin nhắn (timestamp milliseconds) */
  timestamp?: number;
  /** Metadata bổ sung cho tin nhắn */
  metadata?: Record<string, any>;
}

/**
 * Message data cho tin nhắn văn bản
 */
export interface TextMessageData extends BaseMessageData {
  /** Loại tin nhắn: TEXT */
  type: MessageType.TEXT;
  /** Nội dung văn bản của tin nhắn */
  content: string;
  /** Danh sách nút quick reply (tùy chọn) */
  quickReplies?: QuickReplyButtonDto[];
}

/**
 * Message data cho tin nhắn hình ảnh
 */
export interface ImageMessageData extends BaseMessageData {
  /** Loại tin nhắn: IMAGE */
  type: MessageType.IMAGE;
  /** Danh sách file hình ảnh đính kèm */
  attachments: MessageAttachmentDto[];
  /** Chú thích cho hình ảnh (tùy chọn) */
  caption?: string;
}

/**
 * Message data cho tin nhắn file
 */
export interface FileMessageData extends BaseMessageData {
  /** Loại tin nhắn: FILE */
  type: MessageType.FILE;
  /** Danh sách file đính kèm */
  attachments: MessageAttachmentDto[];
  /** Mô tả cho file (tùy chọn) */
  description?: string;
}

/**
 * Message data cho tin nhắn âm thanh
 */
export interface AudioMessageData extends BaseMessageData {
  /** Loại tin nhắn: AUDIO */
  type: MessageType.AUDIO;
  /** Danh sách file âm thanh đính kèm */
  attachments: MessageAttachmentDto[];
  /** Thời lượng âm thanh (giây) */
  duration?: number;
  /** Dữ liệu waveform để hiển thị (mảng số) */
  waveform?: number[];
}

/**
 * Message data cho tin nhắn video
 */
export interface VideoMessageData extends BaseMessageData {
  /** Loại tin nhắn: VIDEO */
  type: MessageType.VIDEO;
  /** Danh sách file video đính kèm */
  attachments: MessageAttachmentDto[];
  /** Thời lượng video (giây) */
  duration?: number;
  /** URL thumbnail của video */
  thumbnail?: string;
}

/**
 * Message data cho tin nhắn vị trí
 */
export interface LocationMessageData extends BaseMessageData {
  /** Loại tin nhắn: LOCATION */
  type: MessageType.LOCATION;
  /** Dữ liệu vị trí địa lý (tọa độ, tên địa điểm) */
  locationData: LocationMessageDto;
}

/**
 * Message data cho tin nhắn liên hệ
 */
export interface ContactMessageData extends BaseMessageData {
  /** Loại tin nhắn: CONTACT */
  type: MessageType.CONTACT;
  /** Thông tin liên hệ (tên, số điện thoại, email) */
  contactData: ContactMessageDto;
}

/**
 * Message data cho sticker
 */
export interface StickerMessageData extends BaseMessageData {
  /** Loại tin nhắn: STICKER */
  type: MessageType.STICKER;
  /** ID của sticker */
  stickerId: string;
  /** ID của gói sticker (tùy chọn) */
  stickerPackId?: string;
  /** Emoji tương ứng với sticker (tùy chọn) */
  emoji?: string;
}

/**
 * Message data cho tin nhắn hệ thống
 */
export interface SystemMessageData extends BaseMessageData {
  /** Loại tin nhắn: SYSTEM */
  type: MessageType.SYSTEM;
  /** Nội dung thông báo hệ thống */
  content: string;
  /** Loại thông báo hệ thống */
  systemType: 'join' | 'leave' | 'update' | 'notification' | 'error';
  /** ID người dùng liên quan (nếu có) */
  relatedUserId?: string;
}

/**
 * Message data cho template message
 */
export interface TemplateMessageData extends BaseMessageData {
  /** Loại tin nhắn: TEMPLATE */
  type: MessageType.TEMPLATE;
  /** Dữ liệu template (ID, tham số, ngôn ngữ) */
  templateData: TemplateMessageDto;
  /** Văn bản dự phòng khi template không hiển thị được */
  fallbackText?: string;
}

/**
 * Message data cho quick reply
 */
export interface QuickReplyMessageData extends BaseMessageData {
  /** Loại tin nhắn: QUICK_REPLY */
  type: MessageType.QUICK_REPLY;
  /** Nội dung tin nhắn kèm quick reply */
  content: string;
  /** Danh sách nút quick reply */
  quickReplies: QuickReplyButtonDto[];
}

/**
 * Message data cho card/carousel
 */
export interface CardMessageData extends BaseMessageData {
  /** Loại tin nhắn: CARD */
  type: MessageType.CARD;
  /** Danh sách các card trong carousel */
  cards: Array<{
    /** Tiêu đề của card */
    title: string;
    /** Phụ đề của card (tùy chọn) */
    subtitle?: string;
    /** URL hình ảnh của card (tùy chọn) */
    imageUrl?: string;
    /** Danh sách nút trong card (tùy chọn) */
    buttons?: Array<{
      /** Loại nút: URL, postback, hoặc số điện thoại */
      type: 'url' | 'postback' | 'phone';
      /** Tiêu đề hiển thị trên nút */
      title: string;
      /** Giá trị của nút (URL, payload, số điện thoại) */
      value: string;
    }>;
  }>;
}

/**
 * Message data cho poll/bình chọn
 */
export interface PollMessageData extends BaseMessageData {
  /** Loại tin nhắn: POLL */
  type: MessageType.POLL;
  /** Câu hỏi của cuộc bình chọn */
  question: string;
  /** Danh sách các lựa chọn */
  options: Array<{
    /** ID của lựa chọn */
    id: string;
    /** Nội dung lựa chọn */
    text: string;
    /** Số lượt bình chọn (tùy chọn) */
    votes?: number;
  }>;
  /** Cho phép chọn nhiều đáp án */
  allowMultipleAnswers?: boolean;
  /** Bình chọn ẩn danh */
  isAnonymous?: boolean;
}

/**
 * Message data cho event
 */
export interface EventMessageData extends BaseMessageData {
  /** Loại tin nhắn: EVENT */
  type: MessageType.EVENT;
  /** Loại sự kiện */
  eventType:
    | 'user_joined'
    | 'user_left'
    | 'conversation_created'
    | 'conversation_updated'
    | 'typing_start'
    | 'typing_stop';
  /** Dữ liệu chi tiết của sự kiện */
  eventData: Record<string, any>;
  /** Mô tả sự kiện (tùy chọn) */
  description?: string;
}

/**
 * Union type cho tất cả message data types
 */
export type MessageData =
  | TextMessageData
  | ImageMessageData
  | FileMessageData
  | AudioMessageData
  | VideoMessageData
  | LocationMessageData
  | ContactMessageData
  | StickerMessageData
  | SystemMessageData
  | TemplateMessageData
  | QuickReplyMessageData
  | CardMessageData
  | PollMessageData
  | EventMessageData;

/**
 * Type guard để kiểm tra loại message data
 */
export function isTextMessageData(data: MessageData): data is TextMessageData {
  return data.type === MessageType.TEXT;
}

export function isImageMessageData(data: MessageData): data is ImageMessageData {
  return data.type === MessageType.IMAGE;
}

export function isFileMessageData(data: MessageData): data is FileMessageData {
  return data.type === MessageType.FILE;
}

export function isAudioMessageData(data: MessageData): data is AudioMessageData {
  return data.type === MessageType.AUDIO;
}

export function isVideoMessageData(data: MessageData): data is VideoMessageData {
  return data.type === MessageType.VIDEO;
}

export function isLocationMessageData(data: MessageData): data is LocationMessageData {
  return data.type === MessageType.LOCATION;
}

export function isContactMessageData(data: MessageData): data is ContactMessageData {
  return data.type === MessageType.CONTACT;
}

export function isStickerMessageData(data: MessageData): data is StickerMessageData {
  return data.type === MessageType.STICKER;
}

export function isSystemMessageData(data: MessageData): data is SystemMessageData {
  return data.type === MessageType.SYSTEM;
}

export function isTemplateMessageData(data: MessageData): data is TemplateMessageData {
  return data.type === MessageType.TEMPLATE;
}

export function isQuickReplyMessageData(data: MessageData): data is QuickReplyMessageData {
  return data.type === MessageType.QUICK_REPLY;
}

export function isCardMessageData(data: MessageData): data is CardMessageData {
  return data.type === MessageType.CARD;
}

export function isPollMessageData(data: MessageData): data is PollMessageData {
  return data.type === MessageType.POLL;
}

export function isEventMessageData(data: MessageData): data is EventMessageData {
  return data.type === MessageType.EVENT;
}