import { Injectable } from '@nestjs/common';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import {
  StructuredTool,
  ToolRunnableConfig,
  ToolSchemaBase,
} from '@langchain/core/tools';
import { z } from 'zod';
import { PageNavigationQueueService } from 'src/shared/queue';
import { NavigationPath } from '../enums/navigation-path.enum';

@Injectable()
export class PageNavigationTool extends StructuredTool {
  name: string;
  description: string;
  schema: ToolSchemaBase;

  constructor(private pageNavService: PageNavigationQueueService) {
    super();
    this.name = 'page_navigation';
    this.description =
      'Navigate the user to a different page in the application. ' +
      'Use this tool when you need to redirect the user to another section or page. ' +
      'You can specify the navigation type: navigate (push to history), redirect (full page reload), or replace (replace current history entry). ' +
      'Available predefined paths with descriptions: ' +
      '"/home" - Trang chủ của ứng dụng, ' +
      '"/contract-affiliate" - <PERSON><PERSON><PERSON><PERSON> lý hợp đồng hợp tác tiếp thị, ' +
      '"/ai-agents" - <PERSON><PERSON><PERSON><PERSON> lý và triển khai các AI agents trong hệ thống, ' +
      '"/data" - Phân tích dữ liệu và báo cáo thống kê, ' +
      '"/calendar" - Quản lý lịch hẹn và sự kiện quan trọng, ' +
      '"/integrations" - Kết nối và tích hợp với các dịch vụ bên thứ ba, ' +
      '"/marketing" - Chiến dịch marketing và các công cụ quảng bá sản phẩm, ' +
      '"/profile" - Thông tin và cấu hình tài khoản cá nhân của người dùng, ' +
      '"/marketplace" - Chợ ứng dụng và tiện ích mở rộng cho hệ thống, ' +
      '"/settings" - Cài đặt cấu hình cho toàn bộ hệ thống, ' +
      '"/rpoint/packages" - Các gói tích điểm thưởng cho khách hàng thân thiết, ' +
      '"/user/affiliate" - Chương trình tiếp thị liên kết và quản lý cộng tác viên, ' +
      '"/business" - Thông tin doanh nghiệp và quản lý hoạt động kinh doanh, ' +
      '"/subscription/packages" - Các gói đăng ký theo tháng hoặc năm dành cho người dùng. ' +
      'You can also use custom paths starting with "/" for dynamic routes like "/products/123".';

    this.schema = z.object({
      path: z
        .union([
          z.nativeEnum(NavigationPath),
          z.string().min(1).startsWith('/'),
        ])
        .describe('The path to navigate to. Available predefined paths'),
      type: z
        .enum(['navigate', 'redirect', 'replace'])
        .optional()
        .default('navigate')
        .describe(
          'Navigation type: navigate (default, pushes to browser history), redirect (full page reload), or replace (replaces current history entry)',
        ),
      message: z
        .string()
        .optional()
        .describe('Optional message to display to the user during navigation'),
      data: z
        .record(z.any())
        .optional()
        .describe(
          'Additional data to pass to the target page (e.g., product details, filter settings)',
        ),
    });
  }

  protected async _call(
    arg: {
      path: string;
      type?: 'navigate' | 'redirect' | 'replace';
      message?: string;
      data?: Record<string, any>;
    },
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<any>,
  ): Promise<string> {
    const { path, type = 'navigate', message, data } = arg;

    // Get user ID from config if available
    const userId = parentConfig?.configurable?.currentUser?.user?.userId;

    try {
      // Validate path format
      if (!path.startsWith('/')) {
        throw new Error('Path must start with "/" (e.g., "/dashboard")');
      }

      const jobId = await this.pageNavService.addNavigateToPageJob({
        path,
        userId,
        type,
        message: message || `Navigating to ${path}`,
        data,
        priority: 'normal',
      });

      if (!jobId) {
        throw new Error('Failed to create navigation job');
      }

      // Log the navigation request
      runManager?.handleText(
        `Navigation job created: ${jobId}\n` +
          `Path: ${path}\n` +
          `Type: ${type}\n` +
          `User: ${userId || 'all users'}`,
      );

      // Return user-friendly message
      const typeMessages = {
        navigate: 'Navigating you',
        redirect: 'Redirecting you',
        replace: 'Taking you',
      };

      return `${typeMessages[type]} to ${path}. ${message || ''}`.trim();
    } catch (error) {
      runManager?.handleToolError(error);
      throw new Error(`Failed to navigate: ${error.message}`);
    }
  }
}
