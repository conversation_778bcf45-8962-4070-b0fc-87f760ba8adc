import { Repository } from 'typeorm';
import { UserConvertCustomerMemory } from '../domains/external/entities/user-convert-customer-memory.entity';
import { UserConvertCustomerInfo, UserInfo } from '../shared/interfaces';
import {
  ZaloPersonalOwnerInfo,
  ZaloPersonalUserInfo,
} from '../platforms/zalo-personal/interfaces/zalo-personal-info.interface';

/**
 * Zalo Personal User Context Prompt Builder (updated for new data structure)
 * Uses ZaloPersonalOwnerInfo and ZaloPersonalUserInfo from the new interface
 */

/**
 * Zalo Personal User Context Only Prompt Builder (without memory)
 * Generates XML representation of Zalo Personal user context for personalized agent responses
 * Uses attribute-heavy approach for minimal token usage
 */
export function buildZaloPersonalUserContextOnlyPrompt(
  zaloPersonalCustomer?: ZaloPersonalUserInfo,
  zaloPersonalOwner?: ZaloPersonalOwnerInfo,
): string {
  if (!zaloPersonalCustomer) {
    return ''; // No Zalo Personal customer info available
  }

  const parts: string[] = ['<zalo-personal-context>'];

  // Zalo Personal user identity section
  const identityAttrs: string[] = [`id="${zaloPersonalCustomer.id}"`];
  if (zaloPersonalCustomer.name)
    identityAttrs.push(`name="${zaloPersonalCustomer.name}"`);
  if (zaloPersonalCustomer.email)
    identityAttrs.push(`email="${zaloPersonalCustomer.email.join(', ')}"`);
  if (zaloPersonalCustomer.phone)
    identityAttrs.push(`phone="${zaloPersonalCustomer.phone}"`);
  if (zaloPersonalCustomer.countryCode)
    identityAttrs.push(`country-code="${zaloPersonalCustomer.countryCode}"`);
  if (zaloPersonalCustomer.address)
    identityAttrs.push(`address="${zaloPersonalCustomer.address}"`);

  if (identityAttrs.length > 0) {
    parts.push(`  <zalo-personal-user ${identityAttrs.join(' ')} />`);
  }

  // Zalo Personal user tags (interests/categories)
  if (zaloPersonalCustomer.tags && zaloPersonalCustomer.tags.length > 0) {
    const tagString = zaloPersonalCustomer.tags.join(',');
    parts.push(`  <interests tags="${tagString}" />`);
  }

  // Zalo Personal user metadata (custom fields)
  if (
    zaloPersonalCustomer.metadata &&
    Object.keys(zaloPersonalCustomer.metadata).length > 0
  ) {
    const metadataEntries = Object.entries(zaloPersonalCustomer.metadata)
      .filter(([key, value]) => value !== null && value !== undefined)
      .map(([key, value]) => {
        const escapedValue = String(value).replace(/"/g, '&quot;');
        return `${key}="${escapedValue}"`;
      })
      .join(' ');

    if (metadataEntries) {
      parts.push(`  <zalo-personal-user-metadata ${metadataEntries} />`);
    }
  }

  // Zalo Personal business owner context (Personal account owner information)
  if (zaloPersonalOwner) {
    const ownerAttrs: string[] = [`owner-id="${zaloPersonalOwner.userId}"`];
    if (zaloPersonalOwner.fullName)
      ownerAttrs.push(`owner-name="${zaloPersonalOwner.fullName}"`);
    if (zaloPersonalOwner.email)
      ownerAttrs.push(`contact-email="${zaloPersonalOwner.email}"`);
    if (zaloPersonalOwner.timezone)
      ownerAttrs.push(`timezone="${zaloPersonalOwner.timezone}"`);
    if (zaloPersonalOwner.currency)
      ownerAttrs.push(`currency="${zaloPersonalOwner.currency}"`);
    if (zaloPersonalOwner.type)
      ownerAttrs.push(`business-type="${zaloPersonalOwner.type}"`);

    parts.push(`  <business ${ownerAttrs.join(' ')} />`);
  }

  // Zalo Personal Account context (from owner credentials)
  if (zaloPersonalOwner?.credentials) {
    const credentials = zaloPersonalOwner.credentials;
    const accountAttrs: string[] = [`session-id="${credentials.sessionId}"`];

    parts.push(`  <zalo-personal-account ${accountAttrs.join(' ')} />`);
  }

  // Zalo Personal user profile from automation-web SDK (if available)
  if (zaloPersonalCustomer.zaloPersonalUserDetail) {
    const userDetail = zaloPersonalCustomer.zaloPersonalUserDetail;
    const detailAttrs: string[] = [];

    if (userDetail.userId) detailAttrs.push(`user-uuid="${userDetail.userId}"`);
    if (userDetail.displayName)
      detailAttrs.push(`display-name="${userDetail.displayName}"`);

    // Add dynamic fields from automation-web SDK
    Object.entries(userDetail)
      .filter(
        ([key, value]) =>
          !['userUuid', 'displayName'].includes(key) &&
          value !== null &&
          value !== undefined,
      )
      .forEach(([key, value]) => {
        const escapedValue = String(value).replace(/"/g, '&quot;');
        detailAttrs.push(`${key}="${escapedValue}"`);
      });

    if (detailAttrs.length > 0) {
      parts.push(`  <zalo-personal-profile ${detailAttrs.join(' ')} />`);
    }
  }

  parts.push('</zalo-personal-context>');
  return parts.join('\n');
}

/**
 * Zalo Personal User Memory Prompt Builder with Repository Access
 * Follows website pattern of directly fetching memory data
 * Generates comprehensive Zalo Personal user context including conversation history
 */
export const buildZaloPersonalUserContextPrompt = async (
  zaloPersonalCustomerId: string,
  userConvertCustomerMemoryRepository: Repository<UserConvertCustomerMemory>,
  zaloPersonalCustomer?: ZaloPersonalUserInfo,
  zaloPersonalOwner?: ZaloPersonalOwnerInfo,
): Promise<string> => {
  if (!zaloPersonalCustomerId) {
    return ''; // No Zalo Personal customer ID, no context
  }

  const parts: string[] = [];

  // Add Zalo Personal user context (without memory)
  const contextPrompt = buildZaloPersonalUserContextOnlyPrompt(
    zaloPersonalCustomer,
    zaloPersonalOwner,
  );
  if (contextPrompt) {
    parts.push(contextPrompt);
  }

  // Fetch and add Zalo Personal user memories
  try {
    const memories = await userConvertCustomerMemoryRepository
      .createQueryBuilder('memory')
      .where('memory.userConvertCustomerId = :zaloPersonalCustomerId', {
        zaloPersonalCustomerId,
      })
      .orderBy('memory.createdAt', 'DESC')
      .limit(10) // Limit to recent memories
      .getMany();

    if (memories.length > 0) {
      const memoryParts: string[] = ['<zalo-personal-user-memories>'];

      memories.forEach((memory) => {
        const attributes: string[] = [`id="${memory.id}"`];

        // Add created date if available (convert timestamp to date)
        if (memory.createdAt && typeof memory.createdAt === 'string') {
          try {
            const timestamp = parseInt(memory.createdAt, 10);
            if (!isNaN(timestamp) && timestamp > 0) {
              const date = new Date(timestamp);
              if (!isNaN(date.getTime())) {
                const dateString = date.toISOString().split('T')[0];
                attributes.push(`created="${dateString}"`);
              }
            }
          } catch (error) {
            // Skip invalid timestamp, continue without date
            console.warn(
              `Invalid timestamp for Zalo Personal user memory ${memory.id}: ${memory.createdAt}`,
            );
          }
        }

        memoryParts.push(`  <memory ${attributes.join(' ')}>`);

        // Content field - main memory content
        if (memory.content) {
          // Escape XML characters in content
          const escapedContent = memory.content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;');

          memoryParts.push(`    <content>${escapedContent}</content>`);
        }

        // Metadata (Zalo Personal user preferences, tags, categories)
        if (memory.metadata && Object.keys(memory.metadata).length > 0) {
          const metadataEntries = Object.entries(memory.metadata)
            .filter(([key, value]) => value !== null && value !== undefined)
            .map(([key, value]) => {
              // Handle different data types appropriately
              let stringValue: string;
              if (typeof value === 'object') {
                stringValue = JSON.stringify(value);
              } else {
                stringValue = String(value);
              }

              // Escape XML characters
              const escapedValue = stringValue
                .replace(/&/g, '&amp;')
                .replace(/"/g, '&quot;');

              return `${key}="${escapedValue}"`;
            })
            .join(' ');

          if (metadataEntries) {
            memoryParts.push(`    <metadata ${metadataEntries} />`);
          }
        }

        memoryParts.push('  </memory>');
      });

      memoryParts.push('</zalo-personal-user-memories>');
      parts.push(memoryParts.join('\n'));
    }
  } catch (error) {
    console.error('Error fetching Zalo Personal user memories:', error);
    // Continue without memories rather than breaking the prompt
  }

  return parts.join('\n\n');
};
