import { Platform } from '../dto/conversation.dto';

/**
 * Base interface cho tất cả platform data
 */
export interface BasePlatformData {
  /** Nền tảng của tin nhắn (zalo_oa, facebook, website, etc.) */
  platform: Platform;
  /** Thời gian <PERSON> (timestamp milliseconds) */
  timestamp?: number;
  /** ID tin nhắn từ platform */
  messageId?: string;
}

/**
 * Platform data cho Zalo Official Account
 */
export interface ZaloOAPlatformData extends BasePlatformData {
  /** Nền tảng: ZALO_OA */
  platform: Platform.ZALO_OA;
  /** ID của Zalo Official Account */
  oaId: string;
  /** ID người dùng Zalo */
  userId: string;
  /** ID tin nhắn từ Zalo */
  messageId?: string;
  /** Loại quota: promotion (quảng cáo) hoặc notification (thông báo) */
  quotaType?: 'promotion' | 'notification';
  /** ID template ZNS (nếu là template message) */
  templateId?: string;
  /** Dữ liệu template ZNS */
  templateData?: Record<string, any>;
  /** ID tracking để theo dõi tin nhắn */
  trackingId?: string;
}

/**
 * Platform data cho Zalo Personal
 */
export interface ZaloPersonalPlatformData extends BasePlatformData {
  /** Nền tảng: ZALO_PERSONAL */
  platform: Platform.ZALO_PERSONAL;
  /** ID người dùng Zalo */
  userId: string;
  /** ID tin nhắn từ Zalo */
  messageId?: string;
  /** ID cuộc trò chuyện */
  conversationId?: string;
  /** Loại tin nhắn từ Zalo */
  messageType?: string;
}

/**
 * Platform data cho Facebook Messenger
 */
export interface FacebookPlatformData extends BasePlatformData {
  /** Nền tảng: FACEBOOK */
  platform: Platform.FACEBOOK;
  /** ID Facebook Page */
  pageId: string;
  /** ID người dùng Facebook */
  userId: string;
  /** ID tin nhắn từ Facebook */
  messageId?: string;
  /** Message ID từ Facebook Messenger API */
  mid?: string;
  /** ID thread/cuộc trò chuyện */
  threadId?: string;
  /** Watermark để đánh dấu tin nhắn đã đọc */
  watermark?: number;
  /** Sequence number của tin nhắn */
  seq?: number;
  /** Dữ liệu messaging từ Facebook webhook */
  messaging?: {
    /** Thông tin người gửi */
    sender: { id: string };
    /** Thông tin người nhận */
    recipient: { id: string };
    /** Thời gian gửi */
    timestamp: number;
  };
}

/**
 * Platform data cho Website
 */
export interface WebsitePlatformData extends BasePlatformData {
  /** Nền tảng: WEBSITE */
  platform: Platform.WEBSITE;
  /** ID phiên làm việc của visitor */
  sessionId: string;
  /** ID visitor (nếu đã xác định) */
  visitorId?: string;
  /** ID tin nhắn từ website */
  messageId?: string;
  /** URL trang web hiện tại */
  pageUrl?: string;
  /** User agent của trình duyệt */
  userAgent?: string;
  /** Địa chỉ IP của visitor */
  ipAddress?: string;
  /** URL trang giới thiệu */
  referrer?: string;
  /** Thông tin trình duyệt */
  browserInfo?: {
    /** Tên trình duyệt */
    name: string;
    /** Phiên bản trình duyệt */
    version: string;
    /** Hệ điều hành */
    os: string;
  };
}

/**
 * Platform data cho Telegram
 */
export interface TelegramPlatformData extends BasePlatformData {
  /** Nền tảng: TELEGRAM */
  platform: Platform.TELEGRAM;
  /** ID chat Telegram */
  chatId: string;
  /** ID người dùng Telegram */
  userId: string;
  /** ID tin nhắn từ Telegram */
  messageId?: string;
  /** ID update từ Telegram webhook */
  updateId?: number;
  /** Loại chat: riêng tư, nhóm, siêu nhóm, kênh */
  chatType?: 'private' | 'group' | 'supergroup' | 'channel';
  /** Token bot Telegram */
  botToken?: string;
}

/**
 * Platform data cho WhatsApp
 */
export interface WhatsAppPlatformData extends BasePlatformData {
  /** Nền tảng: WHATSAPP */
  platform: Platform.WHATSAPP;
  /** ID số điện thoại WhatsApp Business */
  phoneNumberId: string;
  /** WhatsApp ID của người dùng */
  waId: string;
  /** ID tin nhắn từ WhatsApp */
  messageId?: string;
  /** Trạng thái tin nhắn */
  status?: 'sent' | 'delivered' | 'read' | 'failed';
  /** ID tài khoản WhatsApp Business */
  businessAccountId?: string;
  /** Tên hiển thị của người dùng */
  profileName?: string;
}

/**
 * Platform data cho Email
 */
export interface EmailPlatformData extends BasePlatformData {
  /** Nền tảng: EMAIL */
  platform: Platform.EMAIL;
  /** Địa chỉ email */
  emailAddress: string;
  /** ID tin nhắn email */
  messageId?: string;
  /** ID thread email (cho reply) */
  threadId?: string;
  /** Tiêu đề email */
  subject?: string;
  /** ID email được reply */
  inReplyTo?: string;
  /** Danh sách ID email tham chiếu */
  references?: string[];
  /** Headers của email */
  headers?: Record<string, string>;
  /** Danh sách file đính kèm */
  attachments?: Array<{
    /** Tên file */
    filename: string;
    /** Loại nội dung (MIME type) */
    contentType: string;
    /** Kích thước file (bytes) */
    size: number;
    /** Content ID (cho inline images) */
    contentId?: string;
  }>;
}

/**
 * Platform data cho SMS
 */
export interface SMSPlatformData extends BasePlatformData {
  /** Nền tảng: SMS */
  platform: Platform.SMS;
  /** Số điện thoại */
  phoneNumber: string;
  /** ID tin nhắn SMS */
  messageId?: string;
  /** Nhà cung cấp SMS */
  provider?: 'twilio' | 'nexmo' | 'aws_sns' | 'custom';
  /** Trạng thái gửi SMS */
  status?: 'queued' | 'sent' | 'delivered' | 'failed' | 'undelivered';
  /** Mã lỗi (nếu có) */
  errorCode?: string;
  /** Thông báo lỗi (nếu có) */
  errorMessage?: string;
}

/**
 * Platform data cho Internal system
 */
export interface InternalPlatformData extends BasePlatformData {
  /** Nền tảng: INTERNAL */
  platform: Platform.INTERNAL;
  /** ID người dùng nội bộ */
  userId: string;
  /** ID tin nhắn nội bộ */
  messageId?: string;
  /** Loại tin nhắn hệ thống */
  systemType?: 'notification' | 'alert' | 'reminder' | 'broadcast';
  /** Mức độ ưu tiên */
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  /** Phòng ban */
  department?: string;
  /** ID agent xử lý */
  agentId?: string;
}

/**
 * Union type cho tất cả platform data types
 */
export type PlatformData =
  | ZaloOAPlatformData
  | ZaloPersonalPlatformData
  | FacebookPlatformData
  | WebsitePlatformData
  | TelegramPlatformData
  | WhatsAppPlatformData
  | EmailPlatformData
  | SMSPlatformData
  | InternalPlatformData;

/**
 * Type guard functions để kiểm tra loại platform data
 */
export function isZaloOAPlatformData(data: PlatformData): data is ZaloOAPlatformData {
  return data.platform === Platform.ZALO_OA;
}

export function isZaloPersonalPlatformData(data: PlatformData): data is ZaloPersonalPlatformData {
  return data.platform === Platform.ZALO_PERSONAL;
}

export function isFacebookPlatformData(data: PlatformData): data is FacebookPlatformData {
  return data.platform === Platform.FACEBOOK;
}

export function isWebsitePlatformData(data: PlatformData): data is WebsitePlatformData {
  return data.platform === Platform.WEBSITE;
}

export function isTelegramPlatformData(data: PlatformData): data is TelegramPlatformData {
  return data.platform === Platform.TELEGRAM;
}

export function isWhatsAppPlatformData(data: PlatformData): data is WhatsAppPlatformData {
  return data.platform === Platform.WHATSAPP;
}

export function isEmailPlatformData(data: PlatformData): data is EmailPlatformData {
  return data.platform === Platform.EMAIL;
}

export function isSMSPlatformData(data: PlatformData): data is SMSPlatformData {
  return data.platform === Platform.SMS;
}

export function isInternalPlatformData(data: PlatformData): data is InternalPlatformData {
  return data.platform === Platform.INTERNAL;
}

/**
 * Helper function để tạo platform data theo platform
 */
export function createPlatformData<T extends Platform>(
  platform: T,
  data: Omit<Extract<PlatformData, { platform: T }>, 'platform'>,
): Extract<PlatformData, { platform: T }> {
  return {
    platform,
    ...data,
  } as Extract<PlatformData, { platform: T }>;
}

/**
 * Helper function để validate platform data structure
 */
export function validatePlatformData(data: any): data is PlatformData {
  if (!data || typeof data !== 'object') {
    return false;
  }

  if (!Object.values(Platform).includes(data.platform)) {
    return false;
  }

  // Validate required fields based on platform
  switch (data.platform) {
    case Platform.ZALO_OA:
      return !!(data.oaId && data.userId);
    case Platform.ZALO_PERSONAL:
      return !!data.userId;
    case Platform.FACEBOOK:
      return !!(data.pageId && data.userId);
    case Platform.WEBSITE:
      return !!data.sessionId;
    case Platform.TELEGRAM:
      return !!(data.chatId && data.userId);
    case Platform.WHATSAPP:
      return !!(data.phoneNumberId && data.waId);
    case Platform.EMAIL:
      return !!data.emailAddress;
    case Platform.SMS:
      return !!data.phoneNumber;
    case Platform.INTERNAL:
      return !!data.userId;
    default:
      return false;
  }
}