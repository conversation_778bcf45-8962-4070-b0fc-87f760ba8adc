import { Injectable, Logger } from '@nestjs/common';
import { EsmsClient } from '@warriorteam/esms-sdk';

/**
 * Interface cho cấu hình eSMS credentials
 */
export interface ESmsCredentials {
  /**
   * API Key của eSMS
   */
  apiKey: string;

  /**
   * Secret Key của eSMS
   */
  secretKey: string;
}

/**
 * Service để khởi tạo và cung cấp eSMS SDK instance
 * Các service khác sẽ inject service này để sử dụng eSMS SDK
 */
@Injectable()
export class ESmsService {
  readonly providerName = 'eSMS';
  private readonly logger = new Logger(ESmsService.name);
  private readonly esmsClient: EsmsClient;

  constructor() {
    // Khởi tạo EsmsClient với dummy credentials
    // Credentials thật sẽ được truyền vào từng method call
    this.esmsClient = new EsmsClient({
      apiKey: 'dummy',
      secretKey: 'dummy',
      timeoutMs: 30000,
    });

    this.logger.log('eSMS SDK initialized successfully');
  }

  /**
   * Lấy eSMS Client instance
   */
  getClient(): EsmsClient {
    return this.esmsClient;
  }

  /**
   * Lấy SMS Service từ eSMS SDK
   */
  get sms() {
    return this.esmsClient.sms;
  }

  /**
   * Lấy Account Service từ eSMS SDK
   */
  get account() {
    return this.esmsClient.account;
  }

  /**
   * Lấy Zalo Service từ eSMS SDK
   */
  get zalo() {
    return this.esmsClient.zalo;
  }

  /**
   * Lấy Viber Service từ eSMS SDK
   */
  get viber() {
    return this.esmsClient.viber;
  }

  /**
   * Lấy Voice Service từ eSMS SDK
   */
  get voice() {
    return this.esmsClient.voice;
  }

  /**
   * Lấy Multi-channel Service từ eSMS SDK
   */
  get multiChannel() {
    return this.esmsClient.multiChannel;
  }

  /**
   * Lấy Summary Service từ eSMS SDK
   */
  get summary() {
    return this.esmsClient.summary;
  }

  /**
   * Test kết nối với eSMS bằng cách gọi getBalance
   */
  async testConnection(credentials: ESmsCredentials): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      const result = await this.account.getBalance(credentials);

      this.logger.log(`eSMS connection test result:`, result);

      if (result && typeof result.Balance !== 'undefined') {
        if (result.CodeResponse === '100') {
          return {
            success: true,
            message: `Kết nối thành công. Số dư: ${result.Balance} VND`,
            details: result,
          };
        } else if (result.CodeResponse === '101') {
          return {
            success: false,
            message: 'Lỗi xác thực: API Key hoặc Secret Key không đúng',
            details: result,
          };
        } else {
          return {
            success: false,
            message: `Lỗi từ eSMS: ${result.ErrorMessage || 'Unknown error'} (Code: ${result.CodeResponse})`,
            details: result,
          };
        }
      } else {
        return {
          success: false,
          message: 'Không thể lấy thông tin tài khoản - Response không hợp lệ',
          details: result,
        };
      }
    } catch (error) {
      this.logger.error('Error testing eSMS connection:', error);
      return {
        success: false,
        message: 'Kết nối thất bại: ' + error.message,
        details: error.message || 'Unknown error',
      };
    }
  }
}
