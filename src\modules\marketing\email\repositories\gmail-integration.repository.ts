import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../../../../shared/entities/integration.entity';
import { KeyPairEncryptionService } from '../../../../shared/services/encryption/key-pair-encryption.service';

/**
 * Interface cho Gmail server configuration
 */
export interface GmailServerConfig {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  from: string;
  serverType: 'GMAIL';
  accessToken: string;
  refreshToken?: string;
}

/**
 * Repository để xử lý Gmail email integrations
 */
@Injectable()
export class GmailIntegrationRepository {
  private readonly logger = new Logger(GmailIntegrationRepository.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    private readonly encryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * <PERSON><PERSON>y và giải mã Gmail integration
   * @param serverId UUID của integration
   * @returns Gmail server configuration đã giải mã
   */
  async findAndDecryptGmailIntegration(serverId: string): Promise<GmailServerConfig> {
    try {
      this.logger.debug(`Finding Gmail integration for serverId: ${serverId}`);

      // Lấy integration với provider information
      const integration = await this.integrationRepository
        .createQueryBuilder('integration')
        .leftJoinAndSelect('integration.integrationProvider', 'provider')
        .where('integration.id = :serverId', { serverId })
        .andWhere('integration.status = :status', { status: 'active' })
        .andWhere('provider.type = :providerType', { providerType: 'EMAIL_GMAIL' })
        .getOne();

      if (!integration) {
        throw new Error(`Gmail integration không tồn tại hoặc không active: ${serverId}`);
      }

      // Giải mã encrypted config
      if (!integration.encryptedConfig || !integration.secretKey) {
        throw new Error(`Gmail integration thiếu encrypted config hoặc secret key: ${serverId}`);
      }

      const decryptedConfig = this.encryptionService.decryptObject(
        integration.encryptedConfig,
        integration.secretKey,
      ) as any;

      // Parse metadata
      const metadata = integration.metadata || {};

      // Build Gmail configuration
      const config: GmailServerConfig = {
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        user: (metadata as any).email || decryptedConfig.email,
        password: '', // Gmail không dùng password
        from: (metadata as any).email || decryptedConfig.email,
        serverType: 'GMAIL',
        accessToken: decryptedConfig.accessToken,
        refreshToken: decryptedConfig.refreshToken,
      };

      // Validate required fields for Gmail
      if (!config.user || !config.accessToken) {
        throw new Error('Gmail configuration thiếu thông tin bắt buộc: email, accessToken');
      }

      this.logger.debug(`Gmail configuration resolved for serverId: ${serverId}`);
      return config;

    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy Gmail integration cho serverId ${serverId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem serverId có phải là Gmail integration không
   * @param serverId UUID của integration
   * @returns True nếu là Gmail integration
   */
  async isGmailIntegration(serverId: string): Promise<boolean> {
    try {
      const integration = await this.integrationRepository
        .createQueryBuilder('integration')
        .leftJoinAndSelect('integration.integrationProvider', 'provider')
        .where('integration.id = :serverId', { serverId })
        .andWhere('integration.status = :status', { status: 'active' })
        .andWhere('provider.type = :providerType', { providerType: 'EMAIL_GMAIL' })
        .getOne();

      return !!integration;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra Gmail integration cho serverId ${serverId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
