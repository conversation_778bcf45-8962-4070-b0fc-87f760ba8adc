import { ConversationType } from '../dto/conversation.dto';

/**
 * Base conversation metadata - thông tin chuẩn chung
 */
export interface BaseConversationMetadata {
  /** ID cuộc trò chuyện từ platform gốc */
  externalConversationId: string;

  /** Tên hiển thị của cuộc trò chuyện */
  displayName?: string;

  /** Avatar của cuộc trò chuyện (URL hoặc base64) */
  avatar?: string;

  /** Mô tả ngắn về cuộc trò chuyện */
  description?: string;

  /** Loại cuộc trò chuyện */
  type: ConversationType;

  /** Thông tin người tham gia */
  participants?: ConversationParticipant[];

  /** Thông tin về tin nhắn cuối */
  lastMessage?: LastMessageInfo;

  /** Cài đặt cuộc trò chuyện */
  settings?: ConversationSettings;

  /** Tags/nhãn của cuộc trò chuyện */
  tags?: string[];

  /** Thời gian t<PERSON> (timestamp) */
  createdAt: number;

  /** Thời gian cập nhật cuối (timestamp) */
  updatedAt: number;

  /** Thời gian hoạt động cuối (timestamp) */
  lastActivityAt?: number;

  /** Số lượng thành viên */
  memberCount?: number;
}

/**
 * Vai trò người tham gia
 */
export enum ParticipantRole {
  OWNER = 'owner', // Chủ sở hữu
  ADMIN = 'admin', // Quản trị viên
  MEMBER = 'member', // Thành viên
  GUEST = 'guest', // Khách
  BOT = 'bot', // Bot
  AGENT = 'agent', // Agent hỗ trợ
}

/**
 * Trạng thái người tham gia
 */
export enum ParticipantStatus {
  ACTIVE = 'active', // Đang hoạt động
  LEFT = 'left', // Đã rời khỏi
  KICKED = 'kicked', // Bị kick
  BANNED = 'banned', // Bị cấm
  PENDING = 'pending', // Đang chờ phê duyệt
}

/**
 * Thông tin người tham gia cuộc trò chuyện
 */
export interface ConversationParticipant {
  /** ID người tham gia từ platform */
  externalUserId: string;

  /** ID người tham gia trong hệ thống (nếu có) */
  internalUserId?: string;

  /** Tên hiển thị */
  displayName?: string;

  /** Avatar */
  avatar?: string;

  /** Vai trò trong cuộc trò chuyện */
  role: ParticipantRole;

  /** Trạng thái */
  status: ParticipantStatus;

  /** Thời gian tham gia */
  joinedAt: number;

  /** Thời gian rời khỏi (nếu có) */
  leftAt?: number;

  /** Thời gian hoạt động cuối */
  lastActiveAt?: number;

  /** Có đang online không */
  isOnline?: boolean;

  /** Thông tin bổ sung */
  metadata?: {
    phoneNumber?: string;
    email?: string;
    timezone?: string;
    language?: string;
    device?: {
      type?: 'desktop' | 'mobile' | 'tablet';
      os?: string;
      browser?: string;
    };
  };
}

/**
 * Thông tin tin nhắn cuối
 */
export interface LastMessageInfo {
  /** ID tin nhắn */
  messageId: string;

  /** Nội dung tin nhắn (preview) */
  content: string;

  /** Loại tin nhắn */
  type: 'text' | 'image' | 'file' | 'audio' | 'video' | 'sticker' | 'location' | 'other';

  /** ID người gửi */
  senderId: string;

  /** Tên người gửi */
  senderName?: string;

  /** Thời gian gửi */
  timestamp: number;

  /** Trạng thái tin nhắn */
  status?: 'sent' | 'delivered' | 'read' | 'failed';

  /** Có phải tin nhắn từ mình không */
  isSelf?: boolean;
}

/**
 * Cài đặt cuộc trò chuyện
 */
export interface ConversationSettings {
  /** Bật thông báo */
  notificationsEnabled?: boolean;

  /** Tự động trả lời */
  autoReplyEnabled?: boolean;

  /** Template tự động trả lời */
  autoReplyTemplate?: string;

  /** Bật AI assistant */
  aiAssistantEnabled?: boolean;

  /** Cài đặt AI */
  aiSettings?: {
    model?: string;
    systemPrompt?: string;
    temperature?: number;
  };

  /** Thời gian timeout (phút) */
  timeoutMinutes?: number;

  /** Ngôn ngữ mặc định */
  defaultLanguage?: string;

  /** Múi giờ */
  timezone?: string;

  /** Cài đặt bảo mật */
  security?: {
    requireAuth?: boolean;
    encryptMessages?: boolean;
    autoDeleteAfterDays?: number;
  };

  /** Cài đặt tích hợp */
  integrations?: {
    crmEnabled?: boolean;
    crmId?: string;
    analyticsEnabled?: boolean;
    webhookUrl?: string;
  };
}